{% extends "base.html" %}

{% block title %}需求管理 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/">首页</a></li>
<li class="breadcrumb-item active">需求管理</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-list me-2"></i>需求功能点管理
            </h1>
            <div>
                <button class="btn btn-success me-2" onclick="showExtractModal()" id="extractBtn" disabled>
                    <i class="fas fa-magic me-1"></i>提取需求
                </button>
                <button class="btn btn-primary" onclick="showCreateModal()" id="createBtn" disabled>
                    <i class="fas fa-plus me-1"></i>添加需求
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 项目选择提示 -->
<div class="row" id="projectPrompt">
    <div class="col-12">
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            请先在顶部导航栏选择一个项目
        </div>
    </div>
</div>

<!-- 需求列表 -->
<div class="row" id="requirementsSection" style="display: none;">
    <div class="col-12">
        <!-- 搜索和过滤 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="search-box">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索需求编号或描述...">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="typeFilter">
                    <option value="">所有类型</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="priorityFilter">
                    <option value="">所有优先级</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="statusFilter">
                    <option value="">所有状态</option>
                </select>
            </div>
            <div class="col-md-1">
                <select class="form-select" id="pageSizeSelect" onchange="changePageSize()">
                    <option value="10" selected>10条</option>
                    <option value="30">30条</option>
                    <option value="50">50条</option>
                </select>
            </div>
            <div class="col-md-2 text-end">
                <button class="btn btn-outline-secondary" onclick="refreshRequirements()">
                    <i class="fas fa-sync me-1"></i>刷新
                </button>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th>编号</th>
                                <th>标题</th>
                                <th>类型</th>
                                <th>优先级</th>
                                <th>状态</th>
                                <th>章节</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="requirementsTableBody">
                            <!-- 需求列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 批量操作 -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <button class="btn btn-danger btn-sm" onclick="batchDelete()" id="batchDeleteBtn" style="display: none;">
                            <i class="fas fa-trash me-1"></i>批量删除
                        </button>
                        <button class="btn btn-success btn-sm ms-2" onclick="generateTestCases()" id="generateTestBtn" style="display: none;">
                            <i class="fas fa-vial me-1"></i>生成测试用例
                        </button>
                    </div>
                    <div class="col-md-6">
                        <!-- 分页 -->
                        <div id="pagination"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 提取需求模态框 -->
<div class="modal fade" id="extractModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">从文档提取需求</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="documentSelect" class="form-label">选择文档 <span class="text-danger">*</span></label>
                    <select class="form-select" id="documentSelect" required>
                        <option value="">请选择文档</option>
                    </select>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="batchBySection" checked onchange="toggleSectionOptions()">
                        <label class="form-check-label" for="batchBySection">
                            按章节批次处理
                        </label>
                        <div class="form-text">推荐开启，可避免超出大模型上下文长度限制</div>
                    </div>
                </div>
                <div class="mb-3" id="sectionOptions">
                    <label for="sectionSeparator" class="form-label">章节分隔符</label>
                    <select class="form-select" id="sectionSeparator">
                        <option value="###">### (三级标题)</option>
                        <option value="##">## (二级标题)</option>
                        <option value="#"># (一级标题)</option>
                        <option value="####">#### (四级标题)</option>
                        <option value="---">--- (分隔线)</option>
                        <option value="custom">自定义</option>
                    </select>
                    <div class="mt-2" id="customSeparatorDiv" style="display: none;">
                        <input type="text" class="form-control" id="customSeparator" placeholder="输入自定义分隔符">
                    </div>
                    <div class="form-text">选择用于识别文档章节的分隔符</div>
                </div>
                <div class="mb-3">
                    <label for="maxThreads" class="form-label">并发线程数</label>
                    <input type="number" class="form-control" id="maxThreads" min="1" max="10" value="4">
                    <div class="form-text">用于并发处理多个章节，提高提取速度</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="extractRequirements()">开始提取</button>
            </div>
        </div>
    </div>
</div>

<!-- 创建/编辑需求模态框 -->
<div class="modal fade" id="requirementModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="requirementModalTitle">添加需求</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="requirementForm">
                    <input type="hidden" id="requirementId">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="requirementNumber" class="form-label">需求编号</label>
                                <input type="text" class="form-control" id="requirementNumber" placeholder="自动生成">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="requirementSection" class="form-label">所属章节</label>
                                <input type="text" class="form-control" id="requirementSection">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="requirementType" class="form-label">需求类型</label>
                                <select class="form-select" id="requirementType">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="requirementPriority" class="form-label">优先级</label>
                                <select class="form-select" id="requirementPriority">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="requirementStatus" class="form-label">状态</label>
                                <select class="form-select" id="requirementStatus">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="requirementTitle" class="form-label">需求标题 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="requirementTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="requirementDescription" class="form-label">需求描述</label>
                        <textarea class="form-control" id="requirementDescription" rows="4"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveRequirement()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentSearch = '';
let currentFilters = {};
let selectedRequirements = [];

$(document).ready(function() {
    checkCurrentProject();
    loadFilterOptions();
    
    // 搜索输入框事件
    $('#searchInput').on('input', debounce(function() {
        currentSearch = $(this).val();
        currentPage = 1;
        loadRequirements();
    }, 500));
    
    // 过滤器事件
    $('#typeFilter, #priorityFilter, #statusFilter').change(function() {
        updateFilters();
        currentPage = 1;
        loadRequirements();
    });

    // 章节分隔符选择事件
    $('#sectionSeparator').change(handleSeparatorChange);

    // 初始化章节选项显示状态
    toggleSectionOptions();

    // 检查URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const extractFrom = urlParams.get('extract_from');
    if (extractFrom) {
        setTimeout(() => {
            $('#documentSelect').val(extractFrom);
            showExtractModal();
        }, 1000);
    }
});

// 检查当前项目
function checkCurrentProject() {
    $.get('/api/current_project', function(response) {
        if (response.project_id) {
            $('#projectPrompt').hide();
            $('#requirementsSection').show();
            $('#extractBtn, #createBtn').prop('disabled', false);
            loadRequirements();
            loadDocuments();
        } else {
            $('#projectPrompt').show();
            $('#requirementsSection').hide();
            $('#extractBtn, #createBtn').prop('disabled', true);
        }
    });
}

// 加载过滤器选项
function loadFilterOptions() {
    // 加载需求类型
    $.get('/requirements/api/types', function(response) {
        if (response.success) {
            const typeSelect = $('#requirementType, #typeFilter');
            response.data.forEach(type => {
                typeSelect.append(`<option value="${type}">${type}</option>`);
            });
        }
    });
    
    // 加载优先级
    $.get('/requirements/api/priorities', function(response) {
        if (response.success) {
            const prioritySelect = $('#requirementPriority, #priorityFilter');
            response.data.forEach(priority => {
                prioritySelect.append(`<option value="${priority}">${priority}</option>`);
            });
        }
    });
    
    // 加载状态
    $.get('/requirements/api/statuses', function(response) {
        if (response.success) {
            const statusSelect = $('#requirementStatus, #statusFilter');
            response.data.forEach(status => {
                statusSelect.append(`<option value="${status}">${status}</option>`);
            });
        }
    });
}

// 加载文档列表
function loadDocuments() {
    $.get('/documents/api/list?page_size=100', function(response) {
        if (response.success) {
            const select = $('#documentSelect');
            select.find('option:not(:first)').remove();
            response.data.records.forEach(doc => {
                select.append(`<option value="${doc.id}">${doc.name}</option>`);
            });
        }
    });
}

// 更新过滤器
function updateFilters() {
    currentFilters = {};
    const type = $('#typeFilter').val();
    const priority = $('#priorityFilter').val();
    const status = $('#statusFilter').val();
    
    if (type) currentFilters.type = type;
    if (priority) currentFilters.priority = priority;
    if (status) currentFilters.status = status;
}

// 加载需求列表
function loadRequirements() {
    const pageSize = parseInt($('#pageSizeSelect').val()) || 10;
    const params = {
        page: currentPage,
        page_size: pageSize,
        ...currentFilters
    };
    
    if (currentSearch) {
        params.search = currentSearch;
    }
    
    $.get('/requirements/api/list', params, function(response) {
        if (response.success) {
            renderRequirementsTable(response.data.records);
            initPagination('pagination', response.data.total_pages, currentPage, function(page) {
                currentPage = page;
                loadRequirements();
            });
        } else {
            showNotification('加载需求列表失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载需求列表失败', 'error');
    });
}

// 渲染需求表格
function renderRequirementsTable(requirements) {
    const tbody = $('#requirementsTableBody');
    tbody.empty();
    
    if (requirements.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-list fa-2x mb-2"></i><br>
                    暂无需求数据
                </td>
            </tr>
        `);
        return;
    }
    
    requirements.forEach(function(req) {
        const row = $(`
            <tr>
                <td>
                    <input type="checkbox" class="requirement-checkbox" value="${req.id}" onchange="updateSelection()">
                </td>
                <td><strong>${req.number || '-'}</strong></td>
                <td>
                    <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis;">
                        <strong>${req.title || req.description || '-'}</strong>
                        ${req.description && req.title ? '<br><small class="text-muted">' + req.description.substring(0, 20) + (req.description.length > 20 ? '...' : '') + '</small>' : ''}
                    </div>
                </td>
                <td>${getTypeBadge(req.type || '-')}</td>
                <td>${getPriorityBadge(req.priority || '-')}</td>
                <td>${getStatusBadge(req.status || '-')}</td>
                <td><small class="text-muted">${req.section || '-'}</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-warning" onclick="editRequirement('${req.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteRequirement('${req.id}', '${req.number}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 显示提取模态框
function showExtractModal() {
    const modal = new bootstrap.Modal($('#extractModal')[0]);
    modal.show();
}

// 切换章节选项显示
function toggleSectionOptions() {
    const batchBySection = $('#batchBySection').is(':checked');
    const sectionOptions = $('#sectionOptions');

    if (batchBySection) {
        sectionOptions.show();
    } else {
        sectionOptions.hide();
    }
}

// 处理分隔符选择变化
function handleSeparatorChange() {
    const separatorSelect = $('#sectionSeparator').val();
    const customDiv = $('#customSeparatorDiv');

    if (separatorSelect === 'custom') {
        customDiv.show();
    } else {
        customDiv.hide();
    }
}

// 显示创建模态框
function showCreateModal() {
    $('#requirementModalTitle').text('添加需求');
    $('#requirementForm')[0].reset();
    $('#requirementId').val('');
    const modal = new bootstrap.Modal($('#requirementModal')[0]);
    modal.show();
}

// 编辑需求
function editRequirement(requirementId) {
    $.get(`/requirements/api/${requirementId}`, function(response) {
        if (response.success) {
            const req = response.data;
            $('#requirementModalTitle').text('编辑需求');
            $('#requirementId').val(req.id);
            $('#requirementNumber').val(req.number || '');
            $('#requirementSection').val(req.section || '');
            $('#requirementType').val(req.type || '');
            $('#requirementPriority').val(req.priority || '');
            $('#requirementStatus').val(req.status || '');
            $('#requirementTitle').val(req.title || '');
            $('#requirementDescription').val(req.description || '');

            const modal = new bootstrap.Modal($('#requirementModal')[0]);
            modal.show();
        } else {
            showNotification('获取需求信息失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('获取需求信息失败', 'error');
    });
}

// 保存需求
function saveRequirement() {
    const requirementId = $('#requirementId').val();
    const data = {
        number: $('#requirementNumber').val().trim(),
        section: $('#requirementSection').val().trim(),
        type: $('#requirementType').val(),
        priority: $('#requirementPriority').val(),
        status: $('#requirementStatus').val(),
        title: $('#requirementTitle').val().trim(),
        description: $('#requirementDescription').val().trim()
    };

    if (!data.title) {
        showNotification('请输入需求标题', 'error');
        return;
    }

    const url = requirementId ? `/requirements/api/${requirementId}/update` : '/requirements/api/create';
    const method = requirementId ? 'PUT' : 'POST';

    showProgress('正在保存需求...');

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            hideProgress();
            if (response.success) {
                showNotification(response.message, 'success');
                bootstrap.Modal.getInstance($('#requirementModal')[0]).hide();
                loadRequirements();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            hideProgress();
            let errorMessage = '保存需求失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            }
            showNotification(errorMessage, 'error');
        },
        complete: function() {
            setTimeout(hideProgress, 100);
        }
    });
}

// 提取需求
function extractRequirements() {
    const documentId = $('#documentSelect').val();
    const batchBySection = $('#batchBySection').is(':checked');

    if (!documentId) {
        showNotification('请选择文档', 'error');
        return;
    }

    // 获取章节分隔符
    let sectionSeparator = '###'; // 默认值
    if (batchBySection) {
        const separatorSelect = $('#sectionSeparator').val();
        if (separatorSelect === 'custom') {
            sectionSeparator = $('#customSeparator').val().trim();
            if (!sectionSeparator) {
                showNotification('请输入自定义分隔符', 'error');
                return;
            }
        } else {
            sectionSeparator = separatorSelect;
        }
    }

    const maxThreads = parseInt($('#maxThreads').val()) || 4;

    showProgress('正在提取需求功能点...');

    $.ajax({
        url: '/requirements/api/extract',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            document_id: documentId,
            batch_by_sections: batchBySection,
            section_separator: sectionSeparator,
            max_threads: maxThreads
        }),
        success: function(response) {
            hideProgress();
            if (response.success) {
                showNotification(response.message, 'success');
                bootstrap.Modal.getInstance($('#extractModal')[0]).hide();

                // 显示提取结果并询问是否保存
                if (response.data.requirements.length > 0) {
                    if (confirm(`成功提取 ${response.data.total_extracted} 个需求功能点，是否保存到数据库？`)) {
                        saveExtractedRequirements(response.data.requirements);
                    }
                }
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            hideProgress();
            console.error('提取需求错误:', xhr, status, error);

            let errorMessage = '提取需求失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status >= 500) {
                errorMessage = '服务器错误，请稍后重试';
            }

            showNotification(errorMessage, 'error');
        },
        complete: function() {
            setTimeout(hideProgress, 100);
        }
    });
}

// 保存提取的需求
function saveExtractedRequirements(requirements) {
    showProgress('正在保存需求...');
    
    $.ajax({
        url: '/requirements/api/save_extracted',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({requirements: requirements}),
        success: function(response) {
            hideProgress();
            if (response.success) {
                showNotification(response.message, 'success');
                loadRequirements();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            hideProgress();
            showNotification('保存需求失败', 'error');
        },
        complete: function() {
            setTimeout(hideProgress, 100);
        }
    });
}

// 切换全选
function toggleSelectAll() {
    const checked = $('#selectAll').is(':checked');
    $('.requirement-checkbox').prop('checked', checked);
    updateSelection();
}

// 更新选择状态
function updateSelection() {
    selectedRequirements = [];
    $('.requirement-checkbox:checked').each(function() {
        selectedRequirements.push($(this).val());
    });
    
    if (selectedRequirements.length > 0) {
        $('#batchDeleteBtn, #generateTestBtn').show();
    } else {
        $('#batchDeleteBtn, #generateTestBtn').hide();
    }
}

// 生成测试用例
function generateTestCases() {
    if (selectedRequirements.length === 0) {
        showNotification('请选择需求功能点', 'error');
        return;
    }
    
    // 跳转到测试管理页面
    const params = new URLSearchParams();
    params.append('generate_from', selectedRequirements.join(','));
    window.location.href = `/tests?${params.toString()}`;
}

// 刷新页面数据
function refreshPageData() {
    checkCurrentProject();
}

// 改变分页大小
function changePageSize() {
    currentPage = 1; // 重置到第一页
    loadRequirements();
}

// 更新选择状态
function updateSelection() {
    const selectedRequirements = $('.requirement-checkbox:checked');
    const batchDeleteBtn = $('#batchDeleteBtn');
    const generateTestBtn = $('#generateTestBtn');

    if (selectedRequirements.length > 0) {
        batchDeleteBtn.show();
        generateTestBtn.show();
    } else {
        batchDeleteBtn.hide();
        generateTestBtn.hide();
    }

    // 更新全选状态
    const totalCheckboxes = $('.requirement-checkbox').length;
    const checkedCheckboxes = selectedRequirements.length;
    const selectAllCheckbox = $('#selectAll');

    if (checkedCheckboxes === 0) {
        selectAllCheckbox.prop('indeterminate', false);
        selectAllCheckbox.prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        selectAllCheckbox.prop('indeterminate', false);
        selectAllCheckbox.prop('checked', true);
    } else {
        selectAllCheckbox.prop('indeterminate', true);
    }
}

// 切换全选
function toggleSelectAll() {
    const isChecked = $('#selectAll').is(':checked');
    $('.requirement-checkbox').prop('checked', isChecked);
    updateSelection();
}

// 批量删除
function batchDelete() {
    const selectedRequirements = $('.requirement-checkbox:checked');
    if (selectedRequirements.length === 0) {
        showNotification('请选择要删除的需求', 'warning');
        return;
    }

    const requirementIds = [];
    selectedRequirements.each(function() {
        requirementIds.push($(this).val());
    });

    if (confirm(`确定要删除选中的 ${requirementIds.length} 个需求吗？此操作不可恢复。`)) {
        showProgress('正在删除需求...');

        $.ajax({
            url: '/requirements/api/batch_delete',
            method: 'DELETE',
            contentType: 'application/json',
            data: JSON.stringify({requirement_ids: requirementIds}),
            success: function(response) {
                hideProgress();
                if (response.success) {
                    showNotification(response.message, 'success');
                    loadRequirements();
                    $('#selectAll').prop('checked', false);
                    updateSelection();
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                hideProgress();
                let errorMessage = '批量删除失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showNotification(errorMessage, 'error');
            },
            complete: function() {
                setTimeout(hideProgress, 100);
            }
        });
    }
}

// 生成测试用例
function generateTestCases() {
    const selectedRequirements = $('.requirement-checkbox:checked');
    if (selectedRequirements.length === 0) {
        showNotification('请选择要生成测试用例的需求', 'warning');
        return;
    }

    const requirementIds = [];
    selectedRequirements.each(function() {
        requirementIds.push($(this).val());
    });

    // 跳转到测试管理页面
    window.location.href = `/tests?generate_from_requirements=${requirementIds.join(',')}`;
}

// 删除单个需求
function deleteRequirement(requirementId, requirementNumber) {
    if (confirm(`确定要删除需求"${requirementNumber}"吗？`)) {
        $.ajax({
            url: `/requirements/api/${requirementId}/delete`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    loadRequirements();
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function() {
                showNotification('删除需求失败', 'error');
            }
        });
    }
}
</script>
{% endblock %}
