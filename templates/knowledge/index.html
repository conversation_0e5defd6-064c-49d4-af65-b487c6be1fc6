{% extends "base.html" %}

{% block title %}知识库管理 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/">首页</a></li>
<li class="breadcrumb-item active">知识库管理</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-database me-2"></i>知识库管理
            </h1>
            <div>
                <button class="btn btn-success me-2" onclick="autoImportDocuments()" id="autoImportBtn" disabled>
                    <i class="fas fa-download me-1"></i>自动导入文档
                </button>
                <button class="btn btn-primary" onclick="showUploadModal()" id="uploadBtn" disabled>
                    <i class="fas fa-upload me-1"></i>上传文件
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 项目选择提示 -->
<div class="row" id="projectPrompt">
    <div class="col-12">
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            请先在顶部导航栏选择一个项目
        </div>
    </div>
</div>

<!-- 知识库内容 -->
<div class="row" id="knowledgeSection" style="display: none;">
    <!-- 统计信息 -->
    <div class="col-12 mb-4">
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title" id="totalDocuments">0</h5>
                        <p class="card-text">文档数量</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <h5 class="card-title" id="totalChunks">0</h5>
                        <p class="card-text">知识块数量</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">知识库搜索</h6>
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchQuery" placeholder="输入搜索关键词...">
                            <button class="btn btn-outline-primary" onclick="searchKnowledge()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 搜索结果 -->
    <div class="col-12 mb-4" id="searchResults" style="display: none;">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">搜索结果</h6>
            </div>
            <div class="card-body">
                <div id="searchResultsContent">
                    <!-- 搜索结果将在这里显示 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 知识库文档列表 -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">知识库文档</h6>
                    <div>
                        <button class="btn btn-outline-info btn-sm me-2" onclick="testKnowledge()">
                            <i class="fas fa-flask me-1"></i>测试知识库
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="clearKnowledge()">
                            <i class="fas fa-trash me-1"></i>清空知识库
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>文档名称</th>
                                <th>文件类型</th>
                                <th>知识块数量</th>
                                <th>描述</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="knowledgeTableBody">
                            <!-- 知识库文档列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 上传文件模态框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">上传知识库文件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="knowledgeFile" class="form-label">选择文件 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="knowledgeFile" accept=".pdf,.docx,.doc,.md,.txt" required>
                        <div class="form-text">支持格式: PDF, Word, Markdown, 文本文件</div>
                    </div>
                    <div class="mb-3">
                        <label for="knowledgeName" class="form-label">文件名称</label>
                        <input type="text" class="form-control" id="knowledgeName" placeholder="留空则使用文件名">
                    </div>
                    <div class="mb-3">
                        <label for="knowledgeDescription" class="form-label">文件描述</label>
                        <textarea class="form-control" id="knowledgeDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="knowledgeType" class="form-label">文件类型</label>
                        <select class="form-select" id="knowledgeType">
                            <option value="knowledge">知识文档</option>
                            <option value="config">配置文件</option>
                            <option value="script">脚本文件</option>
                            <option value="manual">用户手册</option>
                            <option value="test">测试用例</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadKnowledgeFile()">上传</button>
            </div>
        </div>
    </div>
</div>

<!-- 测试知识库模态框 -->
<div class="modal fade" id="testModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">测试知识库</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">测试查询（每行一个）</label>
                    <textarea class="form-control" id="testQueries" rows="5" placeholder="系统登录功能&#10;用户权限管理&#10;数据备份&#10;安全要求&#10;性能指标"></textarea>
                </div>
                <div id="testResults">
                    <!-- 测试结果将在这里显示 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="runKnowledgeTest()">开始测试</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    checkCurrentProject();
    
    // 文件选择事件
    $('#knowledgeFile').change(function() {
        const file = this.files[0];
        if (file && !$('#knowledgeName').val()) {
            const fileName = file.name.replace(/\.[^/.]+$/, "");
            $('#knowledgeName').val(fileName);
        }
    });
    
    // 搜索框回车事件
    $('#searchQuery').keypress(function(e) {
        if (e.which === 13) {
            searchKnowledge();
        }
    });
});

// 检查当前项目
function checkCurrentProject() {
    $.get('/api/current_project', function(response) {
        if (response.project_id) {
            $('#projectPrompt').hide();
            $('#knowledgeSection').show();
            $('#uploadBtn, #autoImportBtn').prop('disabled', false);
            loadKnowledgeStats();
            loadKnowledgeDocuments();
        } else {
            $('#projectPrompt').show();
            $('#knowledgeSection').hide();
            $('#uploadBtn, #autoImportBtn').prop('disabled', true);
        }
    });
}

// 加载知识库统计信息
function loadKnowledgeStats() {
    $.get('/knowledge/api/stats', function(response) {
        if (response.success) {
            const stats = response.data;
            $('#totalDocuments').text(stats.total_documents || 0);
            $('#totalChunks').text(stats.total_chunks || 0);
        }
    });
}

// 加载知识库文档列表
function loadKnowledgeDocuments() {
    $.get('/knowledge/api/list', function(response) {
        if (response.success) {
            renderKnowledgeTable(response.data);
        } else {
            showNotification('加载知识库文档失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载知识库文档失败', 'error');
    });
}

// 渲染知识库表格
function renderKnowledgeTable(documents) {
    const tbody = $('#knowledgeTableBody');
    tbody.empty();
    
    if (documents.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="5" class="text-center text-muted py-4">
                    <i class="fas fa-database fa-2x mb-2"></i><br>
                    暂无知识库数据
                </td>
            </tr>
        `);
        return;
    }
    
    documents.forEach(function(doc) {
        const row = $(`
            <tr>
                <td>
                    <strong>${doc.name || '未命名'}</strong>
                </td>
                <td>
                    <span class="badge bg-secondary">${doc.file_type || 'knowledge'}</span>
                </td>
                <td>
                    <span class="badge bg-info">${doc.chunk_count || 0}</span>
                </td>
                <td>
                    <small class="text-muted">${doc.description || '暂无描述'}</small>
                </td>
                <td>
                    <button class="btn btn-outline-danger btn-sm" onclick="removeDocument('${doc.document_id}')" title="移除">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 显示上传模态框
function showUploadModal() {
    $('#uploadForm')[0].reset();
    const modal = new bootstrap.Modal($('#uploadModal')[0]);
    modal.show();
}

// 上传知识库文件
function uploadKnowledgeFile() {
    const fileInput = $('#knowledgeFile')[0];
    const file = fileInput.files[0];
    
    if (!file) {
        showNotification('请选择文件', 'error');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('name', $('#knowledgeName').val().trim());
    formData.append('description', $('#knowledgeDescription').val().trim());
    formData.append('file_type', $('#knowledgeType').val());
    
    showProgress('正在上传并处理文件...');
    
    $.ajax({
        url: '/knowledge/api/upload',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            hideProgress();
            if (response.success) {
                showNotification(response.message, 'success');
                bootstrap.Modal.getInstance($('#uploadModal')[0]).hide();
                loadKnowledgeStats();
                loadKnowledgeDocuments();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            hideProgress();
            showNotification('上传文件失败', 'error');
        },
        complete: function() {
            setTimeout(hideProgress, 100);
        }
    });
}

// 自动导入文档
function autoImportDocuments() {
    if (confirm('确定要自动导入项目中的所有需求/设计文档到知识库吗？')) {
        showProgress('正在导入文档到知识库...');
        
        $.ajax({
            url: '/knowledge/api/auto_import',
            method: 'POST',
            success: function(response) {
                hideProgress();
                if (response.success) {
                    showNotification(response.message, 'success');
                    loadKnowledgeStats();
                    loadKnowledgeDocuments();
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function() {
                hideProgress();
                showNotification('自动导入失败', 'error');
            },
            complete: function() {
                setTimeout(hideProgress, 100);
            }
        });
    }
}

// 搜索知识库
function searchKnowledge() {
    const query = $('#searchQuery').val().trim();

    if (!query) {
        showNotification('请输入搜索关键词', 'error');
        return;
    }

    showProgress('正在搜索知识库...');

    $.ajax({
        url: '/knowledge/api/search',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            query: query,
            top_k: 5
        }),
        timeout: 30000, // 30秒超时
        success: function(response) {
            hideProgress();
            if (response && response.success) {
                displaySearchResults(response.data || [], query);
            } else {
                showNotification(response.message || '搜索失败', 'error');
            }
        },
        error: function(xhr, status, error) {
            hideProgress();
            console.error('搜索知识库错误:', xhr, status, error);

            let errorMessage = '搜索失败';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (status === 'timeout') {
                errorMessage = '搜索超时，请稍后重试';
            } else if (xhr.status >= 500) {
                errorMessage = '服务器错误，请稍后重试';
            }

            showNotification(errorMessage, 'error');
        },
        complete: function() {
            // 确保进度框被隐藏
            setTimeout(hideProgress, 100);
        }
    });
}

// 显示搜索结果
function displaySearchResults(results, query) {
    const container = $('#searchResultsContent');
    container.empty();
    
    if (results.length === 0) {
        container.html(`
            <div class="text-center text-muted">
                <i class="fas fa-search fa-2x mb-2"></i><br>
                没有找到相关内容
            </div>
        `);
    } else {
        results.forEach(function(result, index) {
            const similarity = (result.similarity * 100).toFixed(1);
            const item = $(`
                <div class="border rounded p-3 mb-3">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="mb-0">结果 ${index + 1}</h6>
                        <span class="badge bg-primary">${similarity}% 相似度</span>
                    </div>
                    <p class="mb-2">${result.content}</p>
                    <small class="text-muted">
                        来源: ${result.metadata.name || '未知文档'}
                    </small>
                </div>
            `);
            container.append(item);
        });
    }
    
    $('#searchResults').show();
}

// 测试知识库
function testKnowledge() {
    const modal = new bootstrap.Modal($('#testModal')[0]);
    modal.show();
}

// 运行知识库测试
function runKnowledgeTest() {
    const queries = $('#testQueries').val().trim().split('\n').filter(q => q.trim());
    
    if (queries.length === 0) {
        showNotification('请输入测试查询', 'error');
        return;
    }
    
    showProgress('正在测试知识库...');
    
    $.ajax({
        url: '/knowledge/api/test',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            test_queries: queries
        }),
        success: function(response) {
            hideProgress();
            if (response.success) {
                displayTestResults(response.data);
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            hideProgress();
            showNotification('测试失败', 'error');
        },
        complete: function() {
            setTimeout(hideProgress, 100);
        }
    });
}

// 显示测试结果
function displayTestResults(results) {
    const container = $('#testResults');
    container.empty();
    
    results.forEach(function(result) {
        const item = $(`
            <div class="border rounded p-3 mb-3">
                <h6>查询: ${result.query}</h6>
                <p class="mb-2">找到 ${result.results_count} 个相关结果</p>
                <div class="ms-3">
                    ${result.top_results.map((r, i) => `
                        <div class="mb-2">
                            <strong>结果 ${i + 1}:</strong> ${r.content.substring(0, 100)}...
                            <br><small class="text-muted">相似度: ${(r.similarity * 100).toFixed(1)}%</small>
                        </div>
                    `).join('')}
                </div>
            </div>
        `);
        container.append(item);
    });
}

// 移除文档
function removeDocument(documentId) {
    if (confirm('确定要从知识库中移除此文档吗？')) {
        $.ajax({
            url: '/knowledge/api/remove_document',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({document_id: documentId}),
            success: function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    loadKnowledgeStats();
                    loadKnowledgeDocuments();
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function() {
                showNotification('移除文档失败', 'error');
            }
        });
    }
}

// 清空知识库
function clearKnowledge() {
    if (confirm('确定要清空整个知识库吗？此操作不可恢复！')) {
        $.ajax({
            url: '/knowledge/api/clear',
            method: 'POST',
            success: function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    loadKnowledgeStats();
                    loadKnowledgeDocuments();
                    $('#searchResults').hide();
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function() {
                showNotification('清空知识库失败', 'error');
            }
        });
    }
}

// 刷新页面数据
function refreshPageData() {
    checkCurrentProject();
}
</script>
{% endblock %}
