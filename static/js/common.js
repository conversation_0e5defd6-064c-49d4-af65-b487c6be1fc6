// 通用JavaScript函数

// 全局变量
let currentProjectId = null;

// 页面加载完成后执行
$(document).ready(function() {
    // 加载项目列表
    loadProjects();
    
    // 获取当前项目
    getCurrentProject();
    
    // 设置活动菜单项
    setActiveMenuItem();
    
    // 初始化工具提示
    initTooltips();
});

// 加载项目列表
function loadProjects() {
    $.get('/projects/api/list?page_size=100', function(response) {
        if (response.success) {
            const projects = response.data.records;
            const dropdown = $('#projectDropdownMenu');

            // 清除现有项目（保留固定项）
            dropdown.find('.project-item').remove();
            dropdown.find('.project-divider').remove();

            if (projects.length > 0) {
                // 添加分隔线
                dropdown.append('<li class="dropdown-divider project-divider"></li>');

                // 添加项目列表
                projects.forEach(function(project) {
                    const item = $(`
                        <li class="project-item">
                            <a class="dropdown-item" href="#" onclick="selectProject('${project.id}', '${project.name}')">
                                <i class="fas fa-folder me-2"></i>${project.name}
                            </a>
                        </li>
                    `);
                    dropdown.append(item);
                });
            } else {
                // 添加分隔线
                dropdown.append('<li class="dropdown-divider project-divider"></li>');
                dropdown.append('<li class="project-item"><span class="dropdown-item-text text-muted">暂无项目</span></li>');
            }
        } else {
            showNotification('加载项目列表失败: ' + (response.message || '未知错误'), 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('加载项目列表失败:', error);
        showNotification('加载项目列表失败: 网络错误', 'error');
    });
}

// 选择项目
function selectProject(projectId, projectName) {
    $.ajax({
        url: '/api/set_current_project',
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ project_id: projectId }),
        success: function(response) {
            if (response.success) {
                currentProjectId = projectId;
                $('#currentProjectName').text(projectName);
                showNotification(`已切换到项目: ${projectName}`, 'success');
                
                // 刷新当前页面数据
                if (typeof refreshPageData === 'function') {
                    refreshPageData();
                }
            }
        }
    }).fail(function() {
        showNotification('切换项目失败', 'error');
    });
}

// 获取当前项目
function getCurrentProject() {
    $.get('/api/current_project', function(response) {
        if (response.project_id) {
            currentProjectId = response.project_id;
            
            // 获取项目详情
            $.get(`/projects/api/${response.project_id}`, function(projectResponse) {
                if (projectResponse.success) {
                    $('#currentProjectName').text(projectResponse.data.name);
                }
            });
        }
    });
}

// 设置活动菜单项
function setActiveMenuItem() {
    const currentPath = window.location.pathname;
    $('.sidebar .nav-link').removeClass('active');
    
    $('.sidebar .nav-link').each(function() {
        const href = $(this).attr('href');
        if (href && currentPath.startsWith(href) && href !== '/') {
            $(this).addClass('active');
            
            // 展开父级菜单
            const collapse = $(this).closest('.collapse');
            if (collapse.length > 0) {
                collapse.addClass('show');
            }
        }
    });
}

// 切换侧边栏
function toggleSidebar() {
    const sidebar = $('#sidebar');
    const main = $('main');
    
    if (sidebar.hasClass('collapsed')) {
        sidebar.removeClass('collapsed');
        main.removeClass('expanded');
    } else {
        sidebar.addClass('collapsed');
        main.addClass('expanded');
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    const toast = $('#notificationToast');
    const messageElement = $('#notificationMessage');
    const headerIcon = toast.find('.toast-header i');
    
    // 设置消息内容
    messageElement.text(message);
    
    // 设置图标和样式
    headerIcon.removeClass().addClass('fas me-2');
    toast.removeClass('bg-primary bg-success bg-danger bg-warning');
    
    switch (type) {
        case 'success':
            headerIcon.addClass('fa-check-circle text-success');
            break;
        case 'error':
            headerIcon.addClass('fa-exclamation-circle text-danger');
            break;
        case 'warning':
            headerIcon.addClass('fa-exclamation-triangle text-warning');
            break;
        default:
            headerIcon.addClass('fa-info-circle text-primary');
    }
    
    // 显示通知
    const bsToast = new bootstrap.Toast(toast[0]);
    bsToast.show();
}

// 显示进度模态框
function showProgress(message = '正在处理，请稍候...') {
    try {
        $('#progressMessage').text(message);
        $('#progressBar').css('width', '0%');

        // 确保模态框存在
        const modalElement = $('#progressModal')[0];
        if (modalElement) {
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',
                keyboard: false
            });
            modal.show();
        } else {
            console.warn('进度模态框元素不存在');
        }
    } catch (error) {
        console.error('显示进度框时出错:', error);
    }
}

// 更新进度
function updateProgress(percent, message = null) {
    try {
        $('#progressBar').css('width', Math.min(100, Math.max(0, percent)) + '%');
        if (message) {
            $('#progressMessage').text(message);
        }
    } catch (error) {
        console.error('更新进度时出错:', error);
    }
}

// 隐藏进度模态框
function hideProgress() {
    try {
        const modalElement = $('#progressModal')[0];
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }

        // 强制隐藏模态框背景
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('padding-right', '');
    } catch (error) {
        console.error('隐藏进度框时出错:', error);
        // 强制清理
        try {
            $('#progressModal').hide();
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            $('body').css('padding-right', '');
        } catch (e) {
            console.error('强制清理进度框时出错:', e);
        }
    }
}

// 确认对话框
function confirmDialog(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取状态标签
function getStatusBadge(status) {
    const statusMap = {
        '待处理': 'secondary',
        '处理中': 'primary',
        '已完成': 'success',
        '待执行': 'secondary',
        '执行中': 'primary',
        '通过': 'success',
        '失败': 'danger',
        '阻塞': 'warning'
    };
    
    const badgeClass = statusMap[status] || 'secondary';
    return `<span class="badge bg-${badgeClass}">${status}</span>`;
}

// 获取优先级标签
function getPriorityBadge(priority) {
    const priorityMap = {
        '高': 'danger',
        '中': 'warning',
        '低': 'info'
    };
    
    const badgeClass = priorityMap[priority] || 'secondary';
    return `<span class="badge bg-${badgeClass}">${priority}</span>`;
}

// 获取类型标签
function getTypeBadge(type) {
    const typeMap = {
        '功能需求': 'primary',
        '性能需求': 'success',
        '安全需求': 'danger',
        '接口需求': 'info',
        '用户测试用例': 'primary',
        '自动化测试用例': 'success'
    };
    
    const badgeClass = typeMap[type] || 'secondary';
    return `<span class="badge bg-${badgeClass}">${type}</span>`;
}

// 初始化工具提示
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 初始化分页
function initPagination(containerId, totalPages, currentPage, onPageChange) {
    const container = $(`#${containerId}`);
    container.empty();
    
    if (totalPages <= 1) return;
    
    const pagination = $('<nav><ul class="pagination justify-content-center"></ul></nav>');
    const ul = pagination.find('ul');
    
    // 上一页
    const prevDisabled = currentPage <= 1 ? 'disabled' : '';
    ul.append(`
        <li class="page-item ${prevDisabled}">
            <a class="page-link" href="#" data-page="${currentPage - 1}">上一页</a>
        </li>
    `);
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        ul.append('<li class="page-item"><a class="page-link" href="#" data-page="1">1</a></li>');
        if (startPage > 2) {
            ul.append('<li class="page-item disabled"><span class="page-link">...</span></li>');
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === currentPage ? 'active' : '';
        ul.append(`
            <li class="page-item ${activeClass}">
                <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>
        `);
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            ul.append('<li class="page-item disabled"><span class="page-link">...</span></li>');
        }
        ul.append(`<li class="page-item"><a class="page-link" href="#" data-page="${totalPages}">${totalPages}</a></li>`);
    }
    
    // 下一页
    const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
    ul.append(`
        <li class="page-item ${nextDisabled}">
            <a class="page-link" href="#" data-page="${currentPage + 1}">下一页</a>
        </li>
    `);
    
    container.append(pagination);
    
    // 绑定点击事件
    container.find('.page-link').click(function(e) {
        e.preventDefault();
        const page = parseInt($(this).data('page'));
        if (page && page !== currentPage && page >= 1 && page <= totalPages) {
            onPageChange(page);
        }
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
