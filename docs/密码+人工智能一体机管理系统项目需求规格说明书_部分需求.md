# 密码+人工智能一体机管理系统项目需求规格说明书

V1.0

三未信安科技股份有限公司2025 年 04 月 03 日

版本修订说明

<html><body><table><tr><td>版本</td><td>修改内容</td><td>修改人</td><td>修改日期</td><td>审核人</td><td>发布日期</td></tr><tr><td>V1.0</td><td>创建</td><td>万军</td><td>2025.04.03</td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

# 目录

目录.. 3  
1. 简介 .  
1.1. 术语和缩写 ...  
1.2. 参考资料 .. . 8  
2. 产品描述 . .......8  
3. 用户业务场景分析 ....  
4. 与现有产品差异 ..  
5. 约束与限制 .. .10  
6. 需求详细描述 . .10  
6.1. 产品运行环境 10  
6.2. 功能清单 .. 11  
6.3. 功能说明 .. 11  
6.4. 监控组件 ... 12  
6.4.1.监控组件适配 . 12  
PR-F-1001 监控组件安装适配 .. 12  
PR-F-1002 监控组件功能测试 .. 13  
6.4.2.监控组件功能扩展 . 13  
PR-F-1101 AI 一体机特殊指标整理 . . 13  
PR-F-1102 AI 一体机特殊指标获取 . 13  
6.5. 管理系统相关需求 14  
6.5.1. secuLlama 服务管理... 14  
6.2.1.1 secuLlama 服务启动 ... 14  
secuLlama 服务启动. 14  
6.2.1.2 secuLlama 服务停止 ... 14  
secuLlama 服务停止.. 14  
6.2.1.3 secuLlama 服务状态查看 ..... 15  
secuLlama 服务状态查看 .... 15  
6.2.1.4 secuLlama 服务版本查看 ..... 15  
secuLlama 服务版本查看 .. ..... 15  
6.5.2.模型管理 .. . 16  
6.5.2.1. 查看正在运行的模型 .... 16  
查看正在运行的模型 .. ..... 16  
6.5.3.监控管理 . 17  
6.5.3.1. 监控面板开发.. 17  
监控面板开发 . 17  
6.5.3.2. AI 一体机特殊指标梳理... 17  
AI 一体机特殊指标梳理...... 18  
6.5.3.3. 告警管理--本迭代由于不考虑数据库问题，暂不开发 . 18

告警管理 . 18

通过内嵌监控组件的告警管理列表，实现告警功能，包含告警规则、告警联系人、告警列表、邮件服务器配  
置、告警历史等。 18  
6.5.4.安全认证 .... . 18  
6.5.4.1. 基于 Ukey 的认证... 18  
基于 Ukey 的认证 18  
6.5.4.2. 基于配置文件的认证 .. .... 19  
基于配置文件的认证 ... 19  
6. 智能应用统一门户相关需求 . 19  
6.6.1.基础网关相关需求 .... 19  
6.6.1.1. 用户身份和用户类型 0  
用户身份和用户类型 . 20  
6.6.1.2. 门户管理服务 API 代理和鉴权. . 20  
门户管理服务 API 代理和鉴权 .. 20  
6.6.1.3. 门户访问代理和鉴权 .. 21  
门户访问代理和鉴权 . 21  
6.6.1.4. 应用访问代理和鉴权  
应用访问代理和鉴权 .. 21  
6.6.2.门户管理员管理 . 22  
6.6.2.1. 新增门户管理员 22  
新增门户管理员 . 22  
6.6.2.2. 编辑门户管理员. 22  
编辑门户管理员 . ... 22  
6.6.2.3. 删除门户管理员. 23  
删除门户管理员 . 23  
6.6.2.4. 门户管理员列表. 23  
门户管理员列表 .. 23  
6.6.2.5. 门户管理员登录. .24  
门户管理员登录 . 24  
6.6.2.6. 门户管理员退出.. 24  
门户管理员退出 . .24  
6.6.3.门户用户管理 .. 25  
6.6.3.1. 新增用户. 25  
新增用户. 25  
6.6.3.2. 编辑用户.. 26  
编辑用户. . 26  
6.6.3.3. 删除用户.. 26  
删除用户 . 26  
6.6.3.4. 门户用户列表.. .... 26  
门户用户列表 . .. 26  
6.6.3.5. 门户用户登录.. 27  
门户用户登录 .... 27  
6.6.3.6. 门户用户退出... .. 28  
门户用户退出 . 28

6.6.3.7. 启用门户用户. 28启用门户用户 .. .. 286.6.3.8. 禁用门户用户. 29禁用门户用户 .. 296.6.4.门户管理（Dify实例管理） 296.6.4.1. 创建门户. 29创建门户 . 296.6.4.2. 编辑门户信息.. 30编辑门户信息 ..... 306.6.4.3. 门户列表... 30门户列表 .. 306.6.4.4. 启动门户.. 31门户列表... . 316.6.4.5. 停止门户..... . 31停止门户... 316.6.4.6. 删除门户.. 32删除门户 . 326.6.4.7. 跳转管理端.. 32跳转管理端..... . 326.6.4.8. 跳转门户. 33跳转管理端..... 336.6.4.9. 首页配置................. ...... 33首页配置 . 336.6.4.10. 访问控制配置.. 34访问控制配置 . 346.6.5. Dify容器化多实例部署改造.. ...... 346.6.5.1. Dify 多实例部署&网络隔离&目录隔离 34Dify 多实例部署&网络隔离&目录隔离 .. 346.6.5.2. Dify 实例连接外部数据库.... . 错误!未定义书签。Dify 实例连接外部数据库.. . 错误!未定义书签。6.6.5.3. Dify 实例连接外部 Redis.... . 错误!未定义书签。Dify 实例连接外部 Redis.. . 错误!未定义书签。6.6.5.4. 初始化供应商和模型 ... 35初始化供应商和模型 . . 356.6.5.5. Dify 实例自动化部署.... . 35Dify 实例自动化部署 . 356.6.6.应用市场 .. 错误!未定义书签。6.6.6.1. 应用模板管理.... . 错误!未定义书签。应用模板管理...... . 错误!未定义书签。6.6.6.2. 应用模板列表... . 错误!未定义书签。应用模板列表 .. . 错误!未定义书签。6.6.6.3. 应用安装.. . 错误!未定义书签。应用安装... . 错误!未定义书签。6.6.7.统一门 35

6.6.7.1. 访问门户首页... . 35  
访问门户首页 .. ... 35  
6.6.7.2. 用户信息展示. . 36  
门户用户信息 . ... 36  
6.6.7.3. 门户首页对话. . 37  
门户首页对话 .. .. 37  
6.6.7.4. 应用探索（门户应用列表） ... 37  
门户应用列表 . ...... 37  
6.6.7.5. 应用对话页... ... 38  
应用对话页 . .. 38  
6.6.7.6. 用户收藏应用、取消收藏应用 . . 错误!未定义书签。  
用户收藏应用、取消收藏 . . 错误!未定义书签。  
6.7. 接口需求 46  
6.8. 界面需求 ... . 46  
6.9. 性能需求 ... .. 46  
6.10. 可靠性/可用性需求 ... 46  
6.11. 安全性需求 .. 47  
6.1.1.数据脱敏 .. . 44  
6.1.2.数据围栏组件适配 .. 45  
6.12. 可维护性需求 .. 48  
6.13. 工作状态需求 ... 48  
6.14. 结构需求 .. . 49  
6.15. 环保需求 .. .. 49  
6.16. 认证需求 ... .... 50  
6.17. 用户文档需求 ... .. 50  
6.18. 客户特殊需求 .. . 51  
6.19. 法律法规要求 . . 51  
6.20. 国家及行业标准要求 .. . 51  
6.21. 失效模式分析(参见《设计失效模式和影响分析(DFMEA)库》) .. 51  
6.22. 其他需求 . . 51

# 1. 简介

## 1.1. 术语和缩写

<html><body><table><tr><td>编号 名词</td><td>说明</td><td></td></tr><tr><td>1</td><td>CryptoAI OneSystem</td><td>密码+人工智能一体机管理系统</td></tr><tr><td>2</td><td>secuLlama vLLM</td><td>基于Ollama添加安全访问策略之后的大模型运行框 架。 Python 库，大模型推理引擎。</td></tr><tr><td>3 3</td><td>secuLlm</td><td>自研大模型运行框架，实现多vLLM进程服务管理及 API请求转发，并添加安全访问策略。</td></tr><tr><td>5</td><td>Dify</td><td>Dify是一款开源的大语言模型(LLM)应用开发平台。 它融合了后端即服务（Backend as Service)和LLMOps 的理念，使开发者可以快速搭建生产级的生成式AI 应用</td></tr><tr><td>6</td><td>知识库</td><td>知识库（Knowledge）是一系列文档（Documents）的 集合，一个文档内可能包含多组内容分段（Chunks）， 知识库可以被整体集成至一个应用中作为检索上下文 使用，用户可以将企业内部文档、FAQ、规范信息等 内容上传至知识库，知识库会自动进行结构化处理。 当LLM接收到用户的问题后，将首先基于关键词在 知识库内检索内容。知识库将根据关键词，召回相关 度排名较高的内容区块，向LLM提供关键上下文以 辅助其生成更加精准的回答</td></tr><tr><td>6</td><td>应用</td><td>应用是指基于GPT等大语言模型构建的实际场景应 用。通过创建应用，用户可以将智能AI技术应用于 特定的需求，应用类型包括：聊天助手、文档生成应 用、Agent、对话流、工作流。</td></tr><tr><td>7</td><td>聊天助手应用</td><td>基于LLM 构建对话式交互的助手，通过交互式界面， 采用一问一答模式与用户持续对话，可以用在客户服 务、在线教育、医疗保健、金融服务等领域。这些应 用可以帮助组织提高工作效率、减少人工成本和提供 更好的用户体验。</td></tr><tr><td>8</td><td>文本生成应用</td><td>面向文本生成类任务的助手，例如撰写故事、文本分 类、翻译等 能够分解任务、推理思考、调用工具的对话式智能助</td></tr><tr><td>9</td><td>Agent应用</td><td>手 适用于定义等复杂流程的多轮对话场景，具有记忆功</td></tr><tr><td>10</td><td>对话流应用</td><td>能的应用编排方式</td></tr><tr><td>11</td><td>工作流应用</td><td>适用于自动化、批处理等单轮生成类任务的场景的应 用编排方式</td></tr></table></body></html>

## 1.2. 参考资料

包括引用的项目内资料、客户方资料等等。

# 2. 产品描述

三未信安基于密码安全与AI 技术的深厚积累，针对大模型本地化部署中的安全挑战，倾力打造"密码+"人工智能一体机产品。

产品深度融合国产硬件、大模型RAG 框架及全栈式安全防护能力，围绕“硬件+软件+服务”提供身份认证、数据隔离、内容过滤、模型保护等核心功能模块。面向各行业智能化升级需求，形成集智能问答交互、文档分析处理、数据治理优化等多功能于一体的“开箱即用”解决方案。通过对算力资源、算法模型与安全机制的统筹设计，全面兼顾业务效率与合规要求，真正实现模型部署易、管理省、安全强，让企业在智能化转型中稳步前行。

三未信安AI 一体机管理系统用于管理、监控 AI 一体机，利用该系统，可以将后台执行的功能通过页面来提供，方便客户使用、管理、监控一体机。该管理系统的整体架构图如下，核心为应用层：

![](images/d93db1d0ae93ff31f65b35a5e4a52b2eebfa5f874970d977deca86a02f8ea437.jpg)
```text
这张图片展示了一个系统的架构图，从用户接入到硬件层的各个组成部分。系统分为五个主要层次：应用层、模型层、系统层、硬件层和监控组件。

1. **用户接入**：
   - 用户可以通过浏览器、移动应用、社交应用、API Client 和第三方应用等方式接入系统。

2. **应用层**：
   - 包含多个Dify实例（如Dify实例1和Dify实例2），每个实例包含智能助手、工作流和知识库等功能。
   - 还有一个数据围栏模块。
   - 智能应用统一门户提供了基础网关、Dify实例管理、统一门户、应用市场、用户权限管理和围栏管理等功能。

3. **模型层**：
   - 包含SecuLlama和VLLM管理组件两个部分。
   - SecuLlama包括LLM API网关、模型安全、输出安全和模型推理等功能，并支持DeepSeek、Qwen系列和Llama系列等模型。
   - VLLM管理组件包括VLLM管理、模型安全、输出安全和LLM API网关等功能，并支持VLLM1、VLLM2和VLLM3等模型。

4. **系统层**：
   - 包含驱动、CUDA/CANN、数据库、向量数据库和知识库文件透明加密等功能。
   - 运行在操作系统OS上。

5. **硬件层**：
   - 包含GPU/NPU、NIC、DISK、CPU和密码卡等硬件设备。

6. **监控组件**：
   - 提供服务管理、模型管理、监控面板和显卡面板等功能。

7. **一体机管理系统**：
   - 提供服务管理、模型管理、监控面板和显卡面板等功能。

整个系统通过这些层次和组件协同工作，为用户提供智能化的应用和服务。
```


说明：管理系统不涉及利用大模型进行推理、会话等业务，仅用来提供运维功能。具体的业务管理、运营功能敬请期待后续产品。

# 3. 用户业务场景分析

1. 运维场景：通过简单的操作即可将大模型轻松运行起来，提供服务，大大降低运维人员的运维压力，降低AI 使用的难度；2. 监控场景：通过管理系统可以实现对一体机的全面、实时监控，可以快速、准确的检测到设备的实时状态，在问题出现时，能够快速定位问题原因；3. 性能优化场景：由于降低了运维压力，且提供了实时的监控，运维人员可以投入更多的精力到大模型性能提升上，通过直观的监控，可能更加清晰的找到性能瓶颈点；4. 数据脱敏场景：提供一系列针对敏感数据的识别和处置方案，应用多种隐私合规标准，对原始数据进行分级打标、判断敏感级别和实施相应的脱敏处理；5. 数据围栏场景：提供输入输出内容风险检测的能力，帮助用户发现色情、暴力、惊悚、敏感、禁限、辱骂等风险内容或元素，降低人工审核成本，提升内容质量；6. 数据隔离场景：通过多Dify 实例的管理功能+租户管理，为每个租户构建独立知识库，确保数据互不干扰，实现知识库隔离。同时，依据不同租户需求，通过精细化的AI 应用权限管理，保障各租户在使用AI 应用时的数据安全与权限边界明确。

# 4. 与现有产品差异

<html><body><table><tr><td>现有产品功能点</td><td>新需求差异描述</td><td>备注</td></tr><tr><td>当前AI一体机没有管理页面，只 能通过后台命令进行管理；且不具 备监控功能。</td><td>增加管理页面，支持通过页面进行简单 的运维操作；支持监控管理，实现对一 体机状态的实时监控。</td><td></td></tr><tr><td>当前AI一体机不具有敏感数据脱 敏处理的能力，存在敏感数据泄漏 风险。</td><td>支持多种数据格式，支持多种敏感数据 识别规则，支持多种脱敏算法。</td><td></td></tr><tr><td>当前AI大模型仅支持简单的关键 词屏蔽或黑白名单规则，无法处理 复杂语义或变体风险内容。</td><td>针对不良价值观、涉黄、违法犯罪等安 全问题，降低大模型拒答率，支持风险 问题的正向引导和纠偏。</td><td></td></tr></table></body></html>

# 5. 约束与限制

说明在实现时所必须满足的条件和所受的限制，以及相应的原因。如：必须使用或者避免的特定技术、工具、编程语言和数据库、企业策略、政府法规或工业标准。

# 6. 需求详细描述

本项目将分多次迭代实现，第一次迭代重点实现基础的运维与监控，dify 服务管理，智能应用门户，并集成数据脱敏组件与内容安全检测组件：

1. 监控组件的适配（以及开发，考虑是否扩展指标）

2. secuLlama/secuLlm 服务的启停--支持参数传入

3. 模型的展示  
4. 监控的管理  
5. 安全认证  
6. 数据脱敏  
7. 数据围栏  
8.智能应用门户

## 6.1. 产品运行环境

软件环境：  

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0001</td><td>操作系统</td><td>Ubuntu 22.04.4 LTS</td><td>高</td></tr><tr><td>PR-E-0002</td><td>JAVA库</td><td>OpenJDK 17</td><td>高</td></tr><tr><td>PR-E-0003</td><td>Nginx</td><td>sansec/3.20.2</td><td>高</td></tr><tr><td></td><td></td><td>使用公用组件JCE-5.3.3.22版本； 监控组件使用2.1版本？？</td><td>高</td></tr><tr><td></td><td>Python</td><td>python3.10+</td><td></td></tr><tr><td></td><td>Go</td><td></td><td></td></tr></table></body></html>

硬件配置：

<html><body><table><tr><td>需求编号</td><td>名称</td><td>需求描述</td><td>优先级</td></tr><tr><td>PR-E-0004</td><td>AI一体机整 体配置</td><td>CPU: Intel Xeon Gold 6430 32核64高 线程*2 内存：32GB DDR4 RECC*8 硬盘1：480GSATA*2 硬盘2：3.84TNVME*2 GPU: GeForce RTX 4090 24GB *4 电源：2000W*2 Ukey : XT200*3</td><td></td></tr></table></body></html>

## 6.2. 功能清单

1. 监控组件的适配（以及开发，考虑是否扩展指标）

2. secuLlama 服务的启停--支持参数传入

3. 模型的展示  
4. 监控的管理  
5. 安全认证  
6. 数据脱敏  
7. 数据围栏  
8.智能应用门户

## 6.3. 功能说明

![](images/c63310e3dab530d594d362584a9409fb23b862ce9c46b3071217412b0515a8c4.jpg)
```text
这张图片展示了一个系统架构图，描述了不同角色（运维人员、系统管理员、门户管理员和普通用户）在系统中的操作流程和交互方式。以下是详细描述：

### 角色与操作
1. **运维人员**
   - **1-1 登录**：运维人员登录系统。
   - **1-2 启动**：启动运行框架。
   - **1-3 后台执行**：负责管理运行框架的启动、监控硬件的使用情况以及后续添加模型的管理。

2. **系统管理员**
   - **2-1 登录**：系统管理员登录系统。
   - **2-2 创建门户管理员**：创建门户管理员。
   - **2-3 授权、请求转发**：进行授权和请求转发。
   - **2-4 启动**：启动相关组件。
   - **2-5 添加供应商**：添加供应商。

3. **门户管理员**
   - **3-1 登录**：门户管理员登录系统。
   - **3-2 创建普通用户**：创建普通用户。
   - **3-3 登录dify实例，在dify实例内操作**：登录dify实例并进行操作。

4. **普通用户**
   - **4-1 登录**：普通用户登录系统。
   - **4-2 统一门户页面**：访问统一门户页面。
   - **4-3 api调用**：通过API调用进行操作。
   - **4-4 访问具体实例的应用**：访问具体实例的应用。

### 系统组件与流程
1. **nginx**
   - 作为前端服务器，处理用户的登录请求和业务调用。

2. **一体机管理系统进程**
   - 负责启动运行框架、监控硬件情况。

3. **统一web平台**
   - 进行鉴权、转发、管理门户管理员的操作。

4. **智能应用管理&统一门户**
   - 管理智能应用和统一门户。

5. **Dify实例1**
   - 具体的应用实例，可以进行启动、接口调用等操作。

6. **secuLlama/VLLM管理组件**
   - 容器化部署的管理组件。

7. **Deepseek系列模型**
   - 包括Qwen系列模型和Llama系列模型，用于大模型运算。

### 流程说明
- 运维人员启动运行框架后，系统管理员和门户管理员分别进行相应的管理和操作。
- 普通用户通过统一门户页面访问具体的应用实例，并通过API进行调用。
- 整个系统通过nginx进行前端处理，通过统一web平台进行鉴权和转发，最终通过Dify实例和Deepseek系列模型进行具体的应用操作和大模型运算。

这张图清晰地展示了各个角色在系统中的职责和操作流程，以及系统各组件之间的交互关系。
```


## 6.4. 监控组件

6.4.1. 监控组件适配

### PR-F-1001 监控组件安装适配

<html><body><table><tr><td>需求编号</td><td>PR-F-1001</td><td>需求名称</td><td>监控组件安装适配</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1、需要在AI一体机上安装监控组件以及所需的基础运行环境； 2、保证监控组件在AI一体机上稳定运行； 3、提供可监控指标的汇总表。</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>輸入輸出約束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">监控组件安装成功，并可稳定运行</td></tr></table></body></html>

<html><body><table><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>

### PR-F-1002 监控组件功能测试

<html><body><table><tr><td>需求编号</td><td>PR-F-1002</td><td>需求名称</td><td>监控组件功能测试</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">1.测试监控组件是否可以正确获取各项指标； 2.整理指标集，确定哪些指标需要在管理系统上展示，对外输出指标汇总表；</td></tr><tr><td>业务流程</td><td colspan="5">不涉及</td></tr><tr><td>输入输出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">1、输出指标汇总表； 2、监控组件各指标采集正常；</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

### 6.4.2. 监控组件功能扩展

#### PR-F-1101 AI 一体机特殊指标整理

<html><body><table><tr><td>需求编号</td><td>PR-F-1101</td><td></td><td>需求名称AI一体机特殊指标整理</td><td>优先级</td><td>高</td></tr><tr><td>需求描述</td><td colspan="5">收集需要单独采集的指标-显卡的数量、显存、温度、进程的使用情况，还需要考虑 国产GPU的兼容问题，将指标汇总至指标汇总表；</td></tr><tr><td>业务流程</td><td colspan="5">gpu列表，每个gpu对象包含：温度、显存、已用显存、 不涉及</td></tr><tr><td>輪入輸出约束</td><td colspan="5">不涉及</td></tr><tr><td>验收标准</td><td colspan="5">输出指标汇总表</td></tr><tr><td>其它说明</td><td colspan="5">不涉及</td></tr></table></body></html>

#### PR-F-1102 AI 一体机特殊指标获取

<html><body><table><tr><td>需求编号</td><td>PR-F-1102</td><td>需求名称</td><td>AI一体机特殊指标获取</td><td>优先级</td><td>高</td></tr></table></body></html>

<html><body><table><tr><td>需求描述</td><td>根据整理的需要单独处理的指标，研究、评估采集方式，并考虑是否可以集成到监 控组件，如果需要集成到监控组件，需要支持通过监控组件获取指标。</td></tr><tr><td>业务流程</td><td>不涉及</td></tr><tr><td>输入输出约束</td><td>不涉及</td></tr><tr><td>验收标准</td><td>如果可以集成至监控组件，可以通过监控组件获取对应的指标。</td></tr><tr><td>其它说明</td><td>不涉及</td></tr></table></body></html>
