[{"id": "88a2e051-c0fc-49a5-9804-2e75ca74984a", "project_id": "9a7f570b-18f6-49bf-9bfb-c0fc9a566e69", "document_id": "a3c2d3c4-dc28-4b7f-89d6-d126944ae6a3", "number": "REQ-001", "section": "用户管理", "type": "功能需求", "description": "系统应支持用户通过邮箱或手机号注册账户", "priority": "高", "status": "待处理", "created_at": "2025-08-18T12:41:59.120022", "updated_at": "2025-08-18T12:41:59.120022"}, {"id": "52735cc1-dca8-4d65-959c-26b1021d0d38", "project_id": "9a7f570b-18f6-49bf-9bfb-c0fc9a566e69", "document_id": "a3c2d3c4-dc28-4b7f-89d6-d126944ae6a3", "number": "REQ-002", "section": "用户管理", "type": "功能需求", "description": "系统应支持用户通过用户名/邮箱+密码登录", "priority": "高", "status": "待处理", "created_at": "2025-08-18T12:41:59.222137", "updated_at": "2025-08-18T12:41:59.222137"}, {"id": "f8a6def0-8f16-4aa2-b4d9-aeabbc2e219d", "project_id": "9a7f570b-18f6-49bf-9bfb-c0fc9a566e69", "document_id": "a3c2d3c4-dc28-4b7f-89d6-d126944ae6a3", "number": "REQ-003", "section": "商品管理", "type": "功能需求", "description": "用户可以浏览商品列表和详情", "priority": "高", "status": "待处理", "created_at": "2025-08-18T12:41:59.279405", "updated_at": "2025-08-18T12:41:59.279405"}, {"id": "d88d9d9d-b550-4159-93d7-0e952ba35bd2", "project_id": "9a7f570b-18f6-49bf-9bfb-c0fc9a566e69", "document_id": "a3c2d3c4-dc28-4b7f-89d6-d126944ae6a3", "number": "REQ-004", "section": "商品管理", "type": "功能需求", "description": "用户可以通过关键词搜索商品", "priority": "中", "status": "待处理", "created_at": "2025-08-18T12:41:59.677028", "updated_at": "2025-08-18T12:41:59.677028"}, {"id": "885dd9f0-0e26-4bc0-ab1a-3041818fe4eb", "project_id": "9a7f570b-18f6-49bf-9bfb-c0fc9a566e69", "document_id": "a3c2d3c4-dc28-4b7f-89d6-d126944ae6a3", "number": "REQ-005", "section": "购物车管理", "type": "功能需求", "description": "用户可以将商品添加到购物车", "priority": "高", "status": "待处理", "created_at": "2025-08-18T12:41:59.855735", "updated_at": "2025-08-18T12:41:59.855735"}, {"id": "666843d5-805d-4cef-9cd6-c1b11a85612a", "project_id": "9a7f570b-18f6-49bf-9bfb-c0fc9a566e69", "document_id": "a3c2d3c4-dc28-4b7f-89d6-d126944ae6a3", "number": "REQ-006", "section": "订单管理", "type": "功能需求", "description": "用户可以从购物车创建订单", "priority": "高", "status": "待处理", "created_at": "2025-08-18T12:42:00.226363", "updated_at": "2025-08-18T12:42:00.226363"}, {"id": "a9d382de-bc01-4f2f-a3b4-7ed25c97aadb", "project_id": "9a7f570b-18f6-49bf-9bfb-c0fc9a566e69", "document_id": "a3c2d3c4-dc28-4b7f-89d6-d126944ae6a3", "number": "REQ-007", "section": "性能需求", "type": "性能需求", "description": "系统响应时间应在2秒内", "priority": "高", "status": "待处理", "created_at": "2025-08-18T12:42:00.557721", "updated_at": "2025-08-18T12:42:00.557721"}, {"id": "c2e8ea8a-fade-4390-afc4-184203f7f450", "project_id": "9a7f570b-18f6-49bf-9bfb-c0fc9a566e69", "document_id": "a3c2d3c4-dc28-4b7f-89d6-d126944ae6a3", "number": "REQ-008", "section": "安全需求", "type": "安全需求", "description": "用户密码应加密存储", "priority": "高", "status": "待处理", "created_at": "2025-08-18T12:42:00.873163", "updated_at": "2025-08-18T12:42:00.873163"}]