[{"id": "a3c2d3c4-dc28-4b7f-89d6-d126944ae6a3", "project_id": "9a7f570b-18f6-49bf-9bfb-c0fc9a566e69", "name": "系统需求规格说明书", "description": "在线购物系统的详细需求规格说明", "content": "# 在线购物系统需求规格说明书\n\n## 1. 系统概述\n本系统是一个面向C端用户的在线购物平台，提供商品浏览、购买、支付等核心功能。\n\n## 2. 功能需求\n\n### 2.1 用户管理\n- 用户注册：系统应支持用户通过邮箱或手机号注册账户\n- 用户登录：系统应支持用户通过用户名/邮箱+密码登录\n- 密码重置：系统应支持用户通过邮箱重置密码\n- 个人信息管理：用户可以修改个人基本信息\n\n### 2.2 商品管理\n- 商品浏览：用户可以浏览商品列表和详情\n- 商品搜索：用户可以通过关键词搜索商品\n- 商品分类：商品按类别进行组织和展示\n- 商品评价：用户可以对购买的商品进行评价\n\n### 2.3 购物车管理\n- 添加商品：用户可以将商品添加到购物车\n- 修改数量：用户可以修改购物车中商品的数量\n- 删除商品：用户可以从购物车中删除商品\n- 清空购物车：用户可以一键清空购物车\n\n### 2.4 订单管理\n- 创建订单：用户可以从购物车创建订单\n- 订单支付：用户可以选择支付方式完成支付\n- 订单查询：用户可以查看历史订单信息\n- 订单取消：用户可以取消未支付的订单\n\n## 3. 非功能需求\n\n### 3.1 性能需求\n- 系统响应时间应在2秒内\n- 系统应支持1000并发用户\n- 数据库查询响应时间应在500ms内\n\n### 3.2 安全需求\n- 用户密码应加密存储\n- 支付信息应使用HTTPS传输\n- 系统应防范SQL注入攻击\n- 系统应实现用户权限控制\n\n### 3.3 可用性需求\n- 系统可用性应达到99.9%\n- 系统应支持7x24小时运行\n- 系统故障恢复时间应在30分钟内\n", "file_type": ".md", "file_size": 1500, "original_filename": "需求规格说明书.md", "created_at": "2025-08-18T12:41:58.984621", "updated_at": "2025-08-18T12:41:58.984621"}]