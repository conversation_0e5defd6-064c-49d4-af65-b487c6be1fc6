[{"id": "782f88fe-edfa-4e4f-bcdd-cc84c9089cd9", "project_id": "9a7f570b-18f6-49bf-9bfb-c0fc9a566e69", "requirement_ids": ["88a2e051-c0fc-49a5-9804-2e75ca74984a"], "name": "用户注册功能测试", "type": "用户测试用例", "description": "验证用户可以通过邮箱成功注册账户", "preconditions": "用户未注册过账户", "steps": "1. 打开注册页面\n2. 输入有效邮箱地址\n3. 输入符合要求的密码\n4. 点击注册按钮", "expected_result": "注册成功，跳转到登录页面", "priority": "高", "status": "待执行", "created_at": "2025-08-18T12:42:01.333016", "updated_at": "2025-08-18T12:42:01.333016"}, {"id": "a6cce878-d748-4c47-8002-46d2319cd050", "project_id": "9a7f570b-18f6-49bf-9bfb-c0fc9a566e69", "requirement_ids": ["52735cc1-dca8-4d65-959c-26b1021d0d38"], "name": "用户登录功能测试", "type": "用户测试用例", "description": "验证用户可以通过用户名和密码成功登录", "preconditions": "用户已注册账户", "steps": "1. 打开登录页面\n2. 输入正确的用户名\n3. 输入正确的密码\n4. 点击登录按钮", "expected_result": "登录成功，跳转到首页", "priority": "高", "status": "待执行", "created_at": "2025-08-18T12:42:01.863772", "updated_at": "2025-08-18T12:42:01.863772"}, {"id": "48637ac3-c8d7-47f5-9f29-245274f4f0f8", "project_id": "9a7f570b-18f6-49bf-9bfb-c0fc9a566e69", "requirement_ids": ["f8a6def0-8f16-4aa2-b4d9-aeabbc2e219d"], "name": "商品列表浏览测试", "type": "用户测试用例", "description": "验证用户可以正常浏览商品列表", "preconditions": "系统中存在商品数据", "steps": "1. 访问商品列表页面\n2. 查看商品展示\n3. 点击商品查看详情", "expected_result": "商品列表正常显示，点击可查看详情", "priority": "高", "status": "待执行", "created_at": "2025-08-18T12:42:02.588671", "updated_at": "2025-08-18T12:42:02.588671"}]