[{"id": "3bd8fd50-6ea2-4845-8c14-df184604b5b4", "name": "验证GPU数量采集功能", "type": "用户测试用例", "description": "测试监控组件能否正确采集并显示AI一体机中GPU的物理数量", "preconditions": "1. 系统已部署监控组件\n2. AI一体机已安装至少2块GPU\n3. GPU驱动已正确安装", "steps": ["1. 登录监控系统管理界面", "2. 导航至『硬件监控-》GPU监控』页面", "3. 查看GPU列表显示的设备数量", "4. 与物理安装的GPU数量进行比对"], "expected_result": "监控组件显示的GPU数量与实际物理安装数量完全一致", "priority": "高", "project_id": "56ff9433-a18f-403b-be0a-b52938b2707b", "requirement_ids": ["84f256d4-ccd3-41fe-8be4-70a2e71e08bd"], "status": "待执行", "created_at": "2025-08-18T20:16:23.667416", "updated_at": "2025-08-18T20:16:23.667416"}, {"id": "14f0ae37-5490-4f35-b0b5-4a4276b81570", "name": "验证显存使用情况采集", "type": "用户测试用例", "description": "测试监控组件能否准确采集GPU显存使用情况", "preconditions": "1. 系统已部署监控组件\n2. GPU上运行有占用显存的AI训练任务", "steps": ["1. 登录监控系统管理界面", "2. 进入实时监控视图", "3. 选择特定GPU设备查看详细指标", "4. 记录显存总量和已用显存数值", "5. 对比系统dmesg日志或nvidia-smi命令输出"], "expected_result": "监控组件显示的显存使用数据与底层工具采集数据误差不超过5%", "priority": "高", "project_id": "56ff9433-a18f-403b-be0a-b52938b2707b", "requirement_ids": ["84f256d4-ccd3-41fe-8be4-70a2e71e08bd"], "status": "待执行", "created_at": "2025-08-18T20:16:23.690879", "updated_at": "2025-08-18T20:16:23.690879"}, {"id": "ab582122-f4e0-418a-99b1-9dcec63a32c4", "name": "GPU温度异常告警测试", "type": "用户测试用例", "description": "验证监控组件在GPU温度异常时的告警机制", "preconditions": "1. 系统已配置温度阈值告警规则\n2. 可模拟GPU温度升高的测试环境", "steps": ["1. 在监控系统中设置温度阈值告警（如85°C）", "2. 使用压力测试工具提升GPU温度", "3. 观察监控系统告警信息推送情况", "4. 检查告警历史记录"], "expected_result": "当GPU温度超过设定阈值时，系统能在30秒内触发告警并记录完整告警信息", "priority": "高", "project_id": "56ff9433-a18f-403b-be0a-b52938b2707b", "requirement_ids": ["84f256d4-ccd3-41fe-8be4-70a2e71e08bd"], "status": "待执行", "created_at": "2025-08-18T20:16:23.784899", "updated_at": "2025-08-18T20:16:23.784899"}, {"id": "e9625ff5-44b8-4676-88bf-971c339c44f6", "name": "国产GPU兼容性测试", "type": "用户测试用例", "description": "验证监控组件对国产GPU的指标采集能力", "preconditions": "1. 测试环境部署国产GPU硬件\n2. 安装国产GPU配套驱动", "steps": ["1. 在监控系统中刷新硬件设备列表", "2. 检查是否能正确识别国产GPU型号", "3. 查看GPU核心频率、显存带宽等关键指标", "4. 执行30分钟持续监控测试"], "expected_result": "监控组件能稳定采集国产GPU指标，无识别错误或数据异常中断", "priority": "高", "project_id": "56ff9433-a18f-403b-be0a-b52938b2707b", "requirement_ids": ["84f256d4-ccd3-41fe-8be4-70a2e71e08bd"], "status": "待执行", "created_at": "2025-08-18T20:16:23.928957", "updated_at": "2025-08-18T20:16:23.928957"}, {"id": "b8f77079-19c0-4d7c-910e-88560205ac96", "name": "GPU进程使用情况监控", "type": "用户测试用例", "description": "测试监控组件对GPU进程使用情况的采集能力", "preconditions": "1. 系统已部署监控组件\n2. 存在多个占用GPU的进程", "steps": ["1. 在监控系统中查看GPU进程列表", "2. 对比系统ps命令显示的GPU进程信息", "3. 模拟新进程占用GPU资源", "4. 验证监控系统是否实时更新进程列表"], "expected_result": "监控组件能实时显示所有GPU进程，包含进程ID、占用显存、运行时间等关键信息", "priority": "中", "project_id": "56ff9433-a18f-403b-be0a-b52938b2707b", "requirement_ids": ["84f256d4-ccd3-41fe-8be4-70a2e71e08bd"], "status": "待执行", "created_at": "2025-08-18T20:16:24.015355", "updated_at": "2025-08-18T20:16:24.015355"}, {"id": "4131539c-4142-45eb-9389-768a3a6a21fd", "name": "监控组件断连恢复测试", "type": "用户测试用例", "description": "验证监控组件在GPU通信中断后的恢复能力", "preconditions": "1. 系统已部署监控组件\n2. 可模拟GPU通信中断的测试环境", "steps": ["1. 正常运行监控组件并采集GPU指标", "2. 手动断开GPU与监控组件的通信链路", "3. 等待1分钟后恢复通信", "4. 观察监控数据是否恢复采集"], "expected_result": "通信恢复后监控组件能在30秒内重新建立连接并继续正常采集数据", "priority": "中", "project_id": "56ff9433-a18f-403b-be0a-b52938b2707b", "requirement_ids": ["84f256d4-ccd3-41fe-8be4-70a2e71e08bd"], "status": "待执行", "created_at": "2025-08-18T20:16:24.075397", "updated_at": "2025-08-18T20:16:24.075397"}, {"id": "f0ee8d86-f4cb-4ef0-91f1-257a6dc118e6", "name": "国产GPU指标正确汇总", "type": "用户测试用例", "description": "验证国产GPU的温度、显存等指标能正确汇总到指标汇总表", "preconditions": ["已部署支持国产GPU的监控组件", "测试环境中存在至少1块国产GPU设备"], "steps": ["1. 启动监控组件服务", "2. 通过管理界面或命令行触发指标采集", "3. 检查指标汇总表中是否包含国产GPU的温度、显存总量、已用显存等字段", "4. 验证指标数值与实际GPU状态是否一致"], "expected_result": "指标汇总表完整显示国产GPU的温度、显存等指标，数值准确无误", "priority": "高", "project_id": "56ff9433-a18f-403b-be0a-b52938b2707b", "requirement_ids": ["04868f1e-a9a7-4d24-89fc-4a8f90d33e5c"], "status": "待执行", "created_at": "2025-08-18T21:31:13.252567", "updated_at": "2025-08-18T21:31:13.252567"}, {"id": "f1970df2-3322-4d71-b056-c60cb0000b32", "name": "国产GPU兼容性异常处理", "type": "用户测试用例", "description": "验证当使用不兼容的国产GPU型号时系统处理逻辑", "preconditions": ["监控组件已部署", "测试环境中存在不兼容的国产GPU型号"], "steps": ["1. 将不兼容的国产GPU连接到测试系统", "2. 启动监控组件进行指标采集", "3. 检查系统日志和错误提示", "4. 验证指标汇总表中是否标记该GPU为不兼容状态"], "expected_result": "系统输出明确的兼容性警告日志，指标汇总表中标记该GPU为不兼容状态", "priority": "高", "project_id": "56ff9433-a18f-403b-be0a-b52938b2707b", "requirement_ids": ["04868f1e-a9a7-4d24-89fc-4a8f90d33e5c"], "status": "待执行", "created_at": "2025-08-18T21:31:13.361361", "updated_at": "2025-08-18T21:31:13.361361"}, {"id": "db0d7640-80c1-42c0-bc59-5e79f3a97038", "name": "多国产GPU并发采集", "type": "用户测试用例", "description": "验证同时连接多块国产GPU时指标汇总的准确性", "preconditions": ["监控组件已部署", "测试环境中连接4块不同型号的国产GPU"], "steps": ["1. 启动监控组件服务", "2. 触发多GPU并发采集", "3. 检查指标汇总表中每块GPU的独立指标记录", "4. 验证各GPU指标数据隔离性"], "expected_result": "指标汇总表中每块GPU有独立记录，指标数据准确且无交叉干扰", "priority": "中", "project_id": "56ff9433-a18f-403b-be0a-b52938b2707b", "requirement_ids": ["04868f1e-a9a7-4d24-89fc-4a8f90d33e5c"], "status": "待执行", "created_at": "2025-08-18T21:31:13.529892", "updated_at": "2025-08-18T21:31:13.529892"}, {"id": "2772f924-7d3a-4d53-8ab2-e9e0c3bbf750", "name": "GPU进程使用情况采集", "type": "用户测试用例", "description": "验证国产GPU进程使用情况的采集与汇总", "preconditions": ["监控组件已部署", "测试环境中运行至少2个GPU计算进程"], "steps": ["1. 启动监控组件服务", "2. 触发指标采集", "3. 检查指标汇总表中进程使用情况字段", "4. 验证进程名称、占用资源等信息准确性"], "expected_result": "指标汇总表正确显示每个GPU的进程使用情况，包含进程名称、占用显存等信息", "priority": "高", "project_id": "56ff9433-a18f-403b-be0a-b52938b2707b", "requirement_ids": ["04868f1e-a9a7-4d24-89fc-4a8f90d33e5c"], "status": "待执行", "created_at": "2025-08-18T21:31:13.799122", "updated_at": "2025-08-18T21:31:13.799122"}, {"id": "503c779b-fd49-4617-afb8-b8e589e99910", "name": "指标汇总表格式验证", "type": "用户测试用例", "description": "验证指标汇总表的输出格式和字段完整性", "preconditions": ["监控组件已部署", "测试环境中存在国产GPU设备"], "steps": ["1. 执行指标采集操作", "2. 导出指标汇总表", "3. 检查表格格式（CSV/Excel等）", "4. 验证字段是否包含：GPU型号、温度、显存总量、已用显存、进程信息"], "expected_result": "输出文件格式正确，字段完整且符合验收标准要求", "priority": "中", "project_id": "56ff9433-a18f-403b-be0a-b52938b2707b", "requirement_ids": ["04868f1e-a9a7-4d24-89fc-4a8f90d33e5c"], "status": "待执行", "created_at": "2025-08-18T21:31:14.120491", "updated_at": "2025-08-18T21:31:14.120491"}]