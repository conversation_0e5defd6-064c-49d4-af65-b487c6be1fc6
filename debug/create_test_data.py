#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
创建测试数据脚本
为AI智能测试平台创建示例数据
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_test_data():
    """创建测试数据"""
    print("AI智能测试平台 - 创建测试数据")
    print("=" * 50)
    
    try:
        from src.config import Config
        from src.storage import StorageManager
        from src.project_manager import ProjectManager
        from src.requirement_manager import RequirementManager
        
        # 初始化组件
        config = Config()
        storage = StorageManager(config)
        project_manager = ProjectManager(storage)
        requirement_manager = RequirementManager(storage, config)
        
        print("✓ 组件初始化成功")
        
        # 创建示例项目
        project_id = project_manager.create_project(
            "在线购物系统",
            "一个完整的电商平台，包含用户管理、商品管理、订单管理等功能"
        )
        print(f"✓ 创建示例项目: {project_id}")
        
        # 创建示例文档
        doc_data = {
            'project_id': project_id,
            'name': '系统需求规格说明书',
            'description': '在线购物系统的详细需求规格说明',
            'content': """# 在线购物系统需求规格说明书

## 1. 系统概述
本系统是一个面向C端用户的在线购物平台，提供商品浏览、购买、支付等核心功能。

## 2. 功能需求

### 2.1 用户管理
- 用户注册：系统应支持用户通过邮箱或手机号注册账户
- 用户登录：系统应支持用户通过用户名/邮箱+密码登录
- 密码重置：系统应支持用户通过邮箱重置密码
- 个人信息管理：用户可以修改个人基本信息

### 2.2 商品管理
- 商品浏览：用户可以浏览商品列表和详情
- 商品搜索：用户可以通过关键词搜索商品
- 商品分类：商品按类别进行组织和展示
- 商品评价：用户可以对购买的商品进行评价

### 2.3 购物车管理
- 添加商品：用户可以将商品添加到购物车
- 修改数量：用户可以修改购物车中商品的数量
- 删除商品：用户可以从购物车中删除商品
- 清空购物车：用户可以一键清空购物车

### 2.4 订单管理
- 创建订单：用户可以从购物车创建订单
- 订单支付：用户可以选择支付方式完成支付
- 订单查询：用户可以查看历史订单信息
- 订单取消：用户可以取消未支付的订单

## 3. 非功能需求

### 3.1 性能需求
- 系统响应时间应在2秒内
- 系统应支持1000并发用户
- 数据库查询响应时间应在500ms内

### 3.2 安全需求
- 用户密码应加密存储
- 支付信息应使用HTTPS传输
- 系统应防范SQL注入攻击
- 系统应实现用户权限控制

### 3.3 可用性需求
- 系统可用性应达到99.9%
- 系统应支持7x24小时运行
- 系统故障恢复时间应在30分钟内
""",
            'file_type': '.md',
            'file_size': 1500,
            'original_filename': '需求规格说明书.md'
        }
        
        doc_id = storage.create('documents', doc_data, project_id)
        print(f"✓ 创建示例文档: {doc_id}")
        
        # 创建示例需求功能点
        requirements = [
            {
                'project_id': project_id,
                'document_id': doc_id,
                'number': 'REQ-001',
                'section': '用户管理',
                'type': '功能需求',
                'description': '系统应支持用户通过邮箱或手机号注册账户',
                'priority': '高',
                'status': '待处理'
            },
            {
                'project_id': project_id,
                'document_id': doc_id,
                'number': 'REQ-002',
                'section': '用户管理',
                'type': '功能需求',
                'description': '系统应支持用户通过用户名/邮箱+密码登录',
                'priority': '高',
                'status': '待处理'
            },
            {
                'project_id': project_id,
                'document_id': doc_id,
                'number': 'REQ-003',
                'section': '商品管理',
                'type': '功能需求',
                'description': '用户可以浏览商品列表和详情',
                'priority': '高',
                'status': '待处理'
            },
            {
                'project_id': project_id,
                'document_id': doc_id,
                'number': 'REQ-004',
                'section': '商品管理',
                'type': '功能需求',
                'description': '用户可以通过关键词搜索商品',
                'priority': '中',
                'status': '待处理'
            },
            {
                'project_id': project_id,
                'document_id': doc_id,
                'number': 'REQ-005',
                'section': '购物车管理',
                'type': '功能需求',
                'description': '用户可以将商品添加到购物车',
                'priority': '高',
                'status': '待处理'
            },
            {
                'project_id': project_id,
                'document_id': doc_id,
                'number': 'REQ-006',
                'section': '订单管理',
                'type': '功能需求',
                'description': '用户可以从购物车创建订单',
                'priority': '高',
                'status': '待处理'
            },
            {
                'project_id': project_id,
                'document_id': doc_id,
                'number': 'REQ-007',
                'section': '性能需求',
                'type': '性能需求',
                'description': '系统响应时间应在2秒内',
                'priority': '高',
                'status': '待处理'
            },
            {
                'project_id': project_id,
                'document_id': doc_id,
                'number': 'REQ-008',
                'section': '安全需求',
                'type': '安全需求',
                'description': '用户密码应加密存储',
                'priority': '高',
                'status': '待处理'
            }
        ]
        
        req_ids = []
        for req_data in requirements:
            req_id = storage.create('requirements', req_data, project_id)
            req_ids.append(req_id)
        
        print(f"✓ 创建示例需求: {len(req_ids)}个")
        
        # 创建示例测试用例
        test_cases = [
            {
                'project_id': project_id,
                'requirement_ids': [req_ids[0]],  # 用户注册
                'name': '用户注册功能测试',
                'type': '用户测试用例',
                'description': '验证用户可以通过邮箱成功注册账户',
                'preconditions': '用户未注册过账户',
                'steps': '1. 打开注册页面\n2. 输入有效邮箱地址\n3. 输入符合要求的密码\n4. 点击注册按钮',
                'expected_result': '注册成功，跳转到登录页面',
                'priority': '高',
                'status': '待执行'
            },
            {
                'project_id': project_id,
                'requirement_ids': [req_ids[1]],  # 用户登录
                'name': '用户登录功能测试',
                'type': '用户测试用例',
                'description': '验证用户可以通过用户名和密码成功登录',
                'preconditions': '用户已注册账户',
                'steps': '1. 打开登录页面\n2. 输入正确的用户名\n3. 输入正确的密码\n4. 点击登录按钮',
                'expected_result': '登录成功，跳转到首页',
                'priority': '高',
                'status': '待执行'
            },
            {
                'project_id': project_id,
                'requirement_ids': [req_ids[2]],  # 商品浏览
                'name': '商品列表浏览测试',
                'type': '用户测试用例',
                'description': '验证用户可以正常浏览商品列表',
                'preconditions': '系统中存在商品数据',
                'steps': '1. 访问商品列表页面\n2. 查看商品展示\n3. 点击商品查看详情',
                'expected_result': '商品列表正常显示，点击可查看详情',
                'priority': '高',
                'status': '待执行'
            }
        ]
        
        test_ids = []
        for test_data in test_cases:
            test_id = storage.create('test_cases', test_data, project_id)
            test_ids.append(test_id)
        
        print(f"✓ 创建示例测试用例: {len(test_ids)}个")
        
        # 输出统计信息
        stats = project_manager.get_project_stats(project_id)
        print(f"\n项目统计信息:")
        print(f"  - 文档数量: {stats['documents']}")
        print(f"  - 需求数量: {stats['requirements']}")
        print(f"  - 测试用例数量: {stats['test_cases']}")
        
        print(f"\n✅ 测试数据创建完成！")
        print(f"项目ID: {project_id}")
        print(f"可以通过Web界面查看和管理这些数据")
        
        return True
        
    except Exception as e:
        print(f"✗ 创建测试数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = create_test_data()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
