#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Web应用测试脚本
启动简化版的Web应用进行测试
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_test_app():
    """创建测试应用"""
    from flask import Flask, render_template, jsonify
    from src.config import Config
    from src.storage import StorageManager
    from src.project_manager import ProjectManager
    
    app = Flask(__name__, 
                template_folder='../templates',
                static_folder='../static')
    app.secret_key = 'test-secret-key'
    
    # 初始化组件
    config = Config()
    storage = StorageManager(config)
    project_manager = ProjectManager(storage)
    
    @app.route('/')
    def index():
        """主页"""
        try:
            projects = project_manager.list_projects()
            return render_template('index.html', projects=projects['records'])
        except Exception as e:
            return f"错误: {str(e)}"
    
    @app.route('/projects')
    def projects():
        """项目管理页面"""
        try:
            return render_template('projects/index.html')
        except Exception as e:
            return f"错误: {str(e)}"
    
    @app.route('/documents')
    def documents():
        """文档管理页面"""
        try:
            return render_template('documents/index.html')
        except Exception as e:
            return f"错误: {str(e)}"
    
    @app.route('/requirements')
    def requirements():
        """需求管理页面"""
        try:
            return render_template('requirements/index.html')
        except Exception as e:
            return f"错误: {str(e)}"
    
    @app.route('/knowledge')
    def knowledge():
        """知识库管理页面"""
        try:
            return render_template('knowledge/index.html')
        except Exception as e:
            return f"错误: {str(e)}"
    
    @app.route('/api/current_project')
    def get_current_project():
        """获取当前项目"""
        return jsonify({'project_id': None})
    
    @app.route('/projects/api/list')
    def list_projects():
        """获取项目列表"""
        try:
            result = project_manager.list_projects()
            return jsonify({
                'success': True,
                'data': result
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': str(e)
            })
    
    @app.route('/test')
    def test_page():
        """测试页面"""
        return """
        <h1>AI智能测试平台 - 测试页面</h1>
        <p>测试时间: {}</p>
        <h2>功能测试链接:</h2>
        <ul>
            <li><a href="/">主页</a></li>
            <li><a href="/projects">项目管理</a></li>
            <li><a href="/documents">文档管理</a></li>
            <li><a href="/requirements">需求管理</a></li>
            <li><a href="/knowledge">知识库管理</a></li>
        </ul>
        <h2>API测试链接:</h2>
        <ul>
            <li><a href="/api/current_project">当前项目API</a></li>
            <li><a href="/projects/api/list">项目列表API</a></li>
        </ul>
        """.format(datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    return app

def main():
    """主函数"""
    print("AI智能测试平台 - Web应用测试")
    print("=" * 50)
    
    try:
        app = create_test_app()
        print("✓ Web应用创建成功")
        
        print("\n启动Web服务器...")
        print("访问地址: http://127.0.0.1:5000")
        print("测试页面: http://127.0.0.1:5000/test")
        print("\n按 Ctrl+C 停止服务器")
        
        app.run(host='127.0.0.1', port=5000, debug=True)
        
    except Exception as e:
        print(f"✗ Web应用启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    main()
