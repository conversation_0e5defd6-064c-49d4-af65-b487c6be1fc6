#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基础功能测试脚本
测试AI智能测试平台的核心功能
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_config():
    """测试配置模块"""
    print("=" * 50)
    print("测试配置模块")
    print("=" * 50)
    
    try:
        from src.config import Config
        config = Config()
        
        print(f"✓ 配置加载成功")
        print(f"  - 数据路径: {config.DATA_PATH}")
        print(f"  - 上传路径: {config.UPLOAD_FOLDER}")
        print(f"  - 知识库路径: {config.KNOWLEDGE_BASE_PATH}")
        print(f"  - 存储类型: {config.STORAGE_TYPE}")
        print(f"  - 块大小: {config.CHUNK_SIZE}")
        print(f"  - 块重叠: {config.CHUNK_OVERLAP}")
        
        return True
    except Exception as e:
        print(f"✗ 配置模块测试失败: {e}")
        return False

def test_storage():
    """测试存储模块"""
    print("\n" + "=" * 50)
    print("测试存储模块")
    print("=" * 50)
    
    try:
        from src.config import Config
        from src.storage import StorageManager
        
        config = Config()
        storage = StorageManager(config)
        
        print(f"✓ 存储管理器初始化成功")
        
        # 测试项目操作
        test_project = {
            'name': '测试项目',
            'description': '这是一个测试项目'
        }
        
        project_id = storage.create('projects', test_project)
        print(f"✓ 创建项目成功: {project_id}")
        
        # 读取项目
        project = storage.read('projects', project_id)
        print(f"✓ 读取项目成功: {project['name']}")
        
        # 更新项目
        update_data = {'description': '更新后的描述'}
        success = storage.update('projects', project_id, update_data)
        print(f"✓ 更新项目成功: {success}")
        
        # 列出项目
        projects = storage.list('projects', page=1, page_size=10)
        print(f"✓ 列出项目成功: 共{projects['total']}个项目")
        
        # 删除项目
        success = storage.delete('projects', project_id)
        print(f"✓ 删除项目成功: {success}")
        
        return True
    except Exception as e:
        print(f"✗ 存储模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_project_manager():
    """测试项目管理器"""
    print("\n" + "=" * 50)
    print("测试项目管理器")
    print("=" * 50)
    
    try:
        from src.config import Config
        from src.storage import StorageManager
        from src.project_manager import ProjectManager
        
        config = Config()
        storage = StorageManager(config)
        project_manager = ProjectManager(storage)
        
        print(f"✓ 项目管理器初始化成功")
        
        # 创建项目
        project_id = project_manager.create_project('测试项目2', '项目管理器测试项目')
        print(f"✓ 创建项目成功: {project_id}")
        
        # 获取项目详情
        project = project_manager.get_project(project_id)
        print(f"✓ 获取项目详情成功: {project['name']}")
        
        # 获取项目统计
        stats = project_manager.get_project_stats(project_id)
        print(f"✓ 获取项目统计成功: {stats}")
        
        # 验证项目名称
        validation = project_manager.validate_project_name('新项目名称')
        print(f"✓ 项目名称验证成功: {validation}")
        
        # 删除项目
        success = project_manager.delete_project(project_id)
        print(f"✓ 删除项目成功: {success}")
        
        return True
    except Exception as e:
        print(f"✗ 项目管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_document_manager():
    """测试文档管理器"""
    print("\n" + "=" * 50)
    print("测试文档管理器")
    print("=" * 50)
    
    try:
        from src.config import Config
        from src.storage import StorageManager
        from src.document_manager import DocumentManager
        
        config = Config()
        storage = StorageManager(config)
        doc_manager = DocumentManager(storage, config)
        
        print(f"✓ 文档管理器初始化成功")
        
        # 创建测试项目
        project_data = {'name': '文档测试项目', 'description': '用于测试文档功能'}
        project_id = storage.create('projects', project_data)
        
        # 创建文档
        doc_data = {
            'name': '测试文档',
            'description': '这是一个测试文档',
            'content': '这是文档的内容。\n\n第二段内容。',
            'file_type': 'txt',
            'file_size': 100
        }
        
        doc_id = storage.create('documents', {**doc_data, 'project_id': project_id}, project_id)
        print(f"✓ 创建文档成功: {doc_id}")
        
        # 获取文档内容
        doc = storage.read('documents', doc_id, project_id)
        content = doc.get('content', '') if doc else ''
        print(f"✓ 获取文档内容成功: {len(content)}字符")

        # 模拟获取文档章节
        sections = content.split('\n\n') if content else []
        print(f"✓ 获取文档章节成功: {len(sections)}个章节")

        # 删除文档
        success = storage.delete('documents', doc_id, project_id)
        print(f"✓ 删除文档成功: {success}")
        
        # 清理测试项目
        storage.delete('projects', project_id)
        
        return True
    except Exception as e:
        print(f"✗ 文档管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_llm_client():
    """测试LLM客户端（模拟）"""
    print("\n" + "=" * 50)
    print("测试LLM客户端")
    print("=" * 50)
    
    try:
        from src.config import Config
        from src.utils.llm_client import LLMClient
        
        config = Config()
        llm_client = LLMClient(config)
        
        print(f"✓ LLM客户端初始化成功")
        
        # 测试文本解析功能
        test_text = """
        编号：REQ-001
        章节：用户管理
        类型：功能需求
        描述：系统应支持用户注册功能
        优先级：高
        
        编号：REQ-002
        章节：用户管理
        类型：功能需求
        描述：系统应支持用户登录功能
        优先级：高
        """
        
        requirements = llm_client._parse_text_requirements(test_text)
        print(f"✓ 文本需求解析成功: {len(requirements)}个需求")
        
        for req in requirements:
            print(f"  - {req.get('number', 'N/A')}: {req.get('description', 'N/A')}")
        
        return True
    except Exception as e:
        print(f"✗ LLM客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_requirement_manager():
    """测试需求管理器"""
    print("\n" + "=" * 50)
    print("测试需求管理器")
    print("=" * 50)
    
    try:
        from src.config import Config
        from src.storage import StorageManager
        from src.requirement_manager import RequirementManager
        
        config = Config()
        storage = StorageManager(config)
        req_manager = RequirementManager(storage, config)
        
        print(f"✓ 需求管理器初始化成功")
        
        # 创建测试项目
        project_data = {'name': '需求测试项目', 'description': '用于测试需求功能'}
        project_id = storage.create('projects', project_data)
        
        # 创建需求
        req_data = {
            'number': 'REQ-001',
            'section': '用户管理',
            'type': '功能需求',
            'description': '系统应支持用户注册功能',
            'priority': '高',
            'status': '待处理'
        }
        
        req_id = req_manager.create_requirement(project_id, req_data)
        print(f"✓ 创建需求成功: {req_id}")
        
        # 获取需求列表
        requirements = req_manager.list_requirements(project_id)
        print(f"✓ 获取需求列表成功: {requirements['total']}个需求")
        
        # 获取需求统计
        stats = req_manager.get_requirement_statistics(project_id)
        print(f"✓ 获取需求统计成功: {stats}")
        
        # 清理测试数据
        req_manager.delete_requirement(req_id, project_id)
        storage.delete('projects', project_id)
        
        return True
    except Exception as e:
        print(f"✗ 需求管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("AI智能测试平台 - 基础功能测试")
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("配置模块", test_config()))
    test_results.append(("存储模块", test_storage()))
    test_results.append(("项目管理器", test_project_manager()))
    test_results.append(("文档管理器", test_document_manager()))
    test_results.append(("LLM客户端", test_llm_client()))
    test_results.append(("需求管理器", test_requirement_manager()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:<15} {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {passed + failed}个测试, {passed}个通过, {failed}个失败")
    
    if failed == 0:
        print("\n🎉 所有测试通过！系统基础功能正常。")
    else:
        print(f"\n⚠️  有{failed}个测试失败，请检查相关模块。")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
