#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试用例管理修复验证脚本
验证测试用例操作按钮、名称跳转、需求关联等功能的修复
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_test_manager_methods():
    """测试TestManager的方法是否存在"""
    print("1. 测试TestManager方法...")
    try:
        from src.test_manager import TestManager
        
        # 检查关键方法是否存在
        required_methods = [
            'list_test_cases',
            'get_test_case', 
            'create_test_case',
            'update_test_case',
            'delete_test_case',
            'batch_delete_test_cases',
            '_enrich_test_cases_with_requirements'
        ]
        
        for method_name in required_methods:
            assert hasattr(TestManager, method_name), f"缺少方法: {method_name}"
        
        print("   ✅ TestManager方法检查通过")
        return True
    except Exception as e:
        print(f"   ❌ TestManager方法检查失败: {e}")
        return False

def test_test_routes():
    """测试测试用例路由是否存在"""
    print("2. 测试测试用例路由...")
    try:
        # 检查路由文件是否存在必要的API
        with open('src/routes/test_routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_routes = [
            'def get_test_case(',
            'def update_test_case(',
            'def delete_test_case(',
            'def batch_delete_test_cases('
        ]
        
        for route in required_routes:
            assert route in content, f"缺少路由: {route}"
        
        print("   ✅ 测试用例路由检查通过")
        return True
    except Exception as e:
        print(f"   ❌ 测试用例路由检查失败: {e}")
        return False

def test_template_functions():
    """测试模板中的JavaScript函数"""
    print("3. 测试模板JavaScript函数...")
    try:
        with open('templates/tests/index.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_functions = [
            'function viewTest(',
            'function editTest(',
            'function deleteTest(',
            'function executeTest(',
            'function showTestDetails(',
            'function showEditTestModal(',
            'function saveTestChanges('
        ]
        
        for func in required_functions:
            assert func in content, f"缺少JavaScript函数: {func}"
        
        # 检查名称是否可点击
        assert 'onclick="viewTest(' in content, "测试用例名称没有点击事件"
        assert 'class="text-primary"' in content, "测试用例名称没有样式"
        
        # 检查批量删除参数
        assert 'test_case_ids' in content, "批量删除参数名称错误"
        
        print("   ✅ 模板JavaScript函数检查通过")
        return True
    except Exception as e:
        print(f"   ❌ 模板JavaScript函数检查失败: {e}")
        return False

def test_requirement_association():
    """测试需求关联功能"""
    print("4. 测试需求关联功能...")
    try:
        # 检查_enrich_test_cases_with_requirements方法是否存在
        with open('src/test_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查方法定义
        assert 'def _enrich_test_cases_with_requirements(' in content, "缺少_enrich_test_cases_with_requirements方法"

        # 检查方法内容
        assert 'requirement_name' in content, "方法中没有设置requirement_name字段"
        assert 'req.get(\'title\')' in content, "没有正确获取需求标题"
        assert '无关联需求' in content, "没有设置默认的无关联需求文本"

        print("   ✅ 需求关联功能检查通过")
        return True
    except Exception as e:
        print(f"   ❌ 需求关联功能检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("测试用例管理修复验证")
    print("=" * 50)
    
    tests = [
        test_test_manager_methods,
        test_test_routes,
        test_template_functions,
        test_requirement_association
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！测试用例管理修复成功！")
    else:
        print("⚠️  部分测试失败，请检查相关功能")
    
    print("=" * 50)
    print("\n修复内容总结:")
    print("1. ✅ 修复测试用例操作按钮 (查看、编辑、删除、执行)")
    print("2. ✅ 增加测试用例名称点击跳转功能")
    print("3. ✅ 修复测试用例需求关联显示")
    print("4. ✅ 完善测试用例详情和编辑功能")
    
    print("\n建议的下一步测试:")
    print("1. 启动应用: python app.py")
    print("2. 创建项目并上传文档")
    print("3. 提取需求功能点")
    print("4. 生成测试用例")
    print("5. 测试测试用例列表的各项功能")
    print("6. 验证需求关联信息显示正确")

if __name__ == '__main__':
    main()
