#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from dotenv import load_dotenv

class Config:
    """配置管理类"""
    
    def __init__(self):
        load_dotenv()
        
        # 应用配置
        self.APP_HOST = os.getenv('APP_HOST', '127.0.0.1')
        self.APP_PORT = int(os.getenv('APP_PORT', 5000))
        self.APP_DEBUG = os.getenv('APP_DEBUG', 'True').lower() == 'true'
        
        # 数据存储配置
        self.STORAGE_TYPE = os.getenv('STORAGE_TYPE', 'json')
        self.DATA_PATH = os.getenv('DATA_PATH', './data')
        self.PROMPT_PATH = os.getenv('PROMPT_PATH', './prompts')
        
        # LLM配置
        self.OPENAI_API_BASE = os.getenv('OPENAI_API_BASE', '')
        self.OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
        self.OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
        
        # 嵌入模型配置
        self.EMBEDDING_MODEL = os.getenv('EMBEDDING_MODEL', 'text-embedding-ada-002')
        self.EMBEDDING_API_BASE = os.getenv('EMBEDDING_API_BASE', '')
        self.EMBEDDING_API_KEY = os.getenv('EMBEDDING_API_KEY', '')
        
        # 文件上传配置
        self.UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER', './data/uploads')
        self.MAX_CONTENT_LENGTH = int(os.getenv('MAX_CONTENT_LENGTH', 16777216))
        
        # 知识库配置
        self.KNOWLEDGE_BASE_PATH = os.getenv('KNOWLEDGE_BASE_PATH', './data/knowledge_base')
        self.CHUNK_SIZE = int(os.getenv('CHUNK_SIZE', 1000))
        self.CHUNK_OVERLAP = int(os.getenv('CHUNK_OVERLAP', 200))
        self.MARKDOWN_SEPARATOR = os.getenv('MARKDOWN_SEPARATOR', '###')

        # 多线程配置
        self.MAX_THREADS = int(os.getenv('MAX_THREADS', 4))
        
        # 确保目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.DATA_PATH,
            self.UPLOAD_FOLDER,
            self.KNOWLEDGE_BASE_PATH,
            os.path.join(self.DATA_PATH, 'projects'),
            os.path.join(self.DATA_PATH, 'prompts'),
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def get_all(self):
        """获取所有配置"""
        return {
            'APP_HOST': self.APP_HOST,
            'APP_PORT': self.APP_PORT,
            'APP_DEBUG': self.APP_DEBUG,
            'STORAGE_TYPE': self.STORAGE_TYPE,
            'DATA_PATH': self.DATA_PATH,
            'OPENAI_API_BASE': self.OPENAI_API_BASE,
            'OPENAI_API_KEY': self.OPENAI_API_KEY,
            'OPENAI_MODEL': self.OPENAI_MODEL,
            'EMBEDDING_MODEL': self.EMBEDDING_MODEL,
            'EMBEDDING_API_BASE': self.EMBEDDING_API_BASE,
            'EMBEDDING_API_KEY': self.EMBEDDING_API_KEY,
            'UPLOAD_FOLDER': self.UPLOAD_FOLDER,
            'MAX_CONTENT_LENGTH': self.MAX_CONTENT_LENGTH,
            'KNOWLEDGE_BASE_PATH': self.KNOWLEDGE_BASE_PATH,
            'CHUNK_SIZE': self.CHUNK_SIZE,
            'CHUNK_OVERLAP': self.CHUNK_OVERLAP,
            'MARKDOWN_SEPARATOR': self.MARKDOWN_SEPARATOR,
            'MAX_THREADS': self.MAX_THREADS,
        }
    
    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def save_to_env(self):
        """保存配置到.env文件"""
        env_content = f"""# 应用配置
APP_HOST={self.APP_HOST}
APP_PORT={self.APP_PORT}
APP_DEBUG={self.APP_DEBUG}

# 数据存储配置
STORAGE_TYPE={self.STORAGE_TYPE}
DATA_PATH={self.DATA_PATH}

# LLM配置
OPENAI_API_BASE={self.OPENAI_API_BASE}
OPENAI_API_KEY={self.OPENAI_API_KEY}
OPENAI_MODEL={self.OPENAI_MODEL}

# 嵌入模型配置
EMBEDDING_MODEL={self.EMBEDDING_MODEL}
EMBEDDING_API_BASE={self.EMBEDDING_API_BASE}
EMBEDDING_API_KEY={self.EMBEDDING_API_KEY}

# 文件上传配置
UPLOAD_FOLDER={self.UPLOAD_FOLDER}
MAX_CONTENT_LENGTH={self.MAX_CONTENT_LENGTH}

# 知识库配置
KNOWLEDGE_BASE_PATH={self.KNOWLEDGE_BASE_PATH}
CHUNK_SIZE={self.CHUNK_SIZE}
CHUNK_OVERLAP={self.CHUNK_OVERLAP}
MARKDOWN_SEPARATOR={self.MARKDOWN_SEPARATOR}

# 多线程配置
MAX_THREADS={self.MAX_THREADS}
"""
        
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
