#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from typing import Dict, List, Optional
from .storage import StorageManager
from .utils.llm_client import LLMClient
from .config import Config

class TestManager:
    """测试管理器"""
    
    def __init__(self, storage: StorageManager, config:Config):
        self.storage = storage
        self.config = config
        self.llm_client = LLMClient(config)
        self.prompts_path = config.PROMPT_PATH
        
        # 确保提示词目录存在
        os.makedirs(self.prompts_path, exist_ok=True)
        
        # 初始化默认提示词文件
        self._init_default_prompts()
    
    def _init_default_prompts(self):
        """初始化默认提示词文件"""
        test_case_prompt_file = os.path.join(self.prompts_path, 'test_case_generation.txt')
        if not os.path.exists(test_case_prompt_file):
            raise FileNotFoundError(f"未找到提示词文件: {test_case_prompt_file}")
        
        automation_prompt_file = os.path.join(self.prompts_path, 'automation_test_generation.txt')
        if not os.path.exists(automation_prompt_file):
            raise FileNotFoundError(f"未找到提示词文件: {automation_prompt_file}")
    
    def generate_test_cases_from_requirements(self, project_id: str, requirement_ids: List[str],
                                            use_knowledge_base: bool = True, max_threads: int = 4) -> Dict:
        """根据需求功能点生成测试用例"""
        # 获取需求信息
        requirements = []
        for req_id in requirement_ids:
            req = self.storage.read('requirements', req_id, project_id)
            if req:
                requirements.append(req)
        
        if not requirements:
            raise ValueError("没有找到有效的需求功能点")
        
        # 获取知识库上下文
        knowledge_context = ""
        if use_knowledge_base:
            try:
                from .knowledge_manager import KnowledgeManager
                knowledge_manager = KnowledgeManager(self.storage, self.config)
                
                # 构建搜索查询
                search_queries = []
                for req in requirements:
                    search_queries.append(req.get('description', ''))
                
                # 搜索相关知识
                all_results = []
                for query in search_queries[:3]:  # 限制搜索次数
                    if query.strip():
                        results = knowledge_manager.search_knowledge_base(project_id, query, top_k=2)
                        all_results.extend(results)
                
                # 构建知识库上下文
                if all_results:
                    context_parts = []
                    for result in all_results[:5]:  # 限制上下文长度
                        context_parts.append(result['content'])
                    knowledge_context = '\n\n'.join(context_parts)
                
            except Exception as e:
                print(f"获取知识库上下文失败: {e}")
        
        # 读取提示词
        system_prompt = self.load_test_case_prompt()
        
        try:
            if max_threads > 1 and len(requirements) > 1:
                # 多线程并发处理
                result = self._generate_test_cases_multithreaded(
                    requirements, knowledge_context, system_prompt, project_id, max_threads
                )

                # 自动保存多线程生成的测试用例
                if result['success'] and result['test_cases']:
                    try:
                        saved_ids = self.save_generated_test_cases(result['test_cases'])
                        result['saved_ids'] = saved_ids
                        result['saved_count'] = len(saved_ids)
                        print(f"自动保存了 {len(saved_ids)} 个测试用例")
                    except Exception as e:
                        print(f"自动保存测试用例失败: {e}")
                        result['save_error'] = str(e)
            else:
                # 单线程处理
                test_cases = self.llm_client.generate_test_cases(
                    requirements, knowledge_context, system_prompt
                )

                # 为每个测试用例添加项目和需求关联信息
                for test_case in test_cases:
                    test_case['project_id'] = project_id
                    test_case['requirement_ids'] = requirement_ids
                    test_case['status'] = '待执行'
                    test_case['type'] = test_case.get('type', '用户测试用例')

                result = {
                    'success': True,
                    'test_cases': test_cases,
                    'total_generated': len(test_cases)
                }

            # 自动保存生成的测试用例
            if result['success'] and result['test_cases']:
                try:
                    saved_ids = self.save_generated_test_cases(result['test_cases'])
                    result['saved_ids'] = saved_ids
                    result['saved_count'] = len(saved_ids)
                    print(f"自动保存了 {len(saved_ids)} 个测试用例")
                except Exception as e:
                    print(f"自动保存测试用例失败: {e}")
                    # 不影响生成结果，只是保存失败
                    result['save_error'] = str(e)

            return result

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'test_cases': []
            }
    
    def generate_automation_test_cases(self, project_id: str, user_test_case_ids: List[str]) -> Dict:
        """根据用户测试用例生成自动化测试用例"""
        # 获取用户测试用例
        user_test_cases = []
        for case_id in user_test_case_ids:
            case = self.storage.read('test_cases', case_id, project_id)
            if case and case.get('type') == '用户测试用例':
                user_test_cases.append(case)
        
        if not user_test_cases:
            raise ValueError("没有找到有效的用户测试用例")
        
        # 读取提示词
        system_prompt = self.load_automation_prompt()
        
        try:
            # 生成自动化测试用例
            auto_test_cases = self.llm_client.generate_automation_test_cases(
                user_test_cases, system_prompt
            )
            
            # 为每个自动化测试用例添加项目信息
            for test_case in auto_test_cases:
                test_case['project_id'] = project_id
                test_case['status'] = '待执行'
                test_case['type'] = '自动化测试用例'
                test_case['parent_test_case_ids'] = user_test_case_ids
            
            return {
                'success': True,
                'test_cases': auto_test_cases,
                'total_generated': len(auto_test_cases)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'test_cases': []
            }
    
    def save_generated_test_cases(self, test_cases: List[Dict]) -> List[str]:
        """保存生成的测试用例"""
        saved_ids = []
        
        for test_case in test_cases:
            try:
                case_id = self.storage.create('test_cases', test_case, test_case['project_id'])
                saved_ids.append(case_id)
            except Exception as e:
                print(f"保存测试用例失败: {e}")
        
        return saved_ids
    
    def create_test_case(self, project_id: str, data: Dict) -> str:
        """创建测试用例"""
        data['project_id'] = project_id
        data['status'] = data.get('status', '待执行')
        data['type'] = data.get('type', '用户测试用例')
        
        return self.storage.create('test_cases', data, project_id)
    
    def get_test_case(self, test_case_id: str, project_id: str = None) -> Optional[Dict]:
        """获取测试用例详情"""
        test_case = self.storage.read('test_cases', test_case_id, project_id)
        if test_case:
            # 为测试用例添加需求信息
            self._enrich_test_cases_with_requirements([test_case], project_id)
        return test_case
    
    def update_test_case(self, test_case_id: str, data: Dict, project_id: str = None) -> bool:
        """更新测试用例"""
        return self.storage.update('test_cases', test_case_id, data, project_id)
    
    def delete_test_case(self, test_case_id: str, project_id: str = None) -> bool:
        """删除测试用例"""
        return self.storage.delete('test_cases', test_case_id, project_id)
    
    def list_test_cases(self, project_id: str, page: int = 1, page_size: int = 20,
                       filters: Dict = None, search: str = None) -> Dict:
        """获取测试用例列表"""
        if search:
            # 搜索测试用例
            test_cases = self.storage.search('test_cases', search, project_id,
                                           fields=['name', 'description', 'steps'])

            # 应用过滤器
            if filters:
                filtered_cases = []
                for case in test_cases:
                    match = True
                    for key, value in filters.items():
                        if value and case.get(key) != value:
                            match = False
                            break
                    if match:
                        filtered_cases.append(case)
                test_cases = filtered_cases

            total = len(test_cases)
            start = (page - 1) * page_size
            end = start + page_size
            page_cases = test_cases[start:end]

            # 为每个测试用例添加需求信息
            self._enrich_test_cases_with_requirements(page_cases, project_id)

            return {
                'records': page_cases,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
        else:
            result = self.storage.list('test_cases', project_id=project_id,
                                     filters=filters, page=page, page_size=page_size)

            # 为每个测试用例添加需求信息
            if result and 'records' in result:
                self._enrich_test_cases_with_requirements(result['records'], project_id)

            return result

    def _enrich_test_cases_with_requirements(self, test_cases: List[Dict], project_id: str):
        """为测试用例添加需求信息"""
        try:
            # 获取所有需求
            requirements_result = self.storage.list('requirements', project_id=project_id, page_size=1000)
            requirements = requirements_result.get('records', [])

            # 创建需求ID到需求信息的映射
            req_map = {}
            for req in requirements:
                req_map[req['id']] = req

            # 为每个测试用例添加需求信息
            for test_case in test_cases:
                requirement_ids = test_case.get('requirement_ids', [])
                if requirement_ids:
                    # 获取关联的需求名称
                    req_names = []
                    for req_id in requirement_ids:
                        if req_id in req_map:
                            req = req_map[req_id]
                            name = req.get('title') or req.get('description', '未命名需求')[:30]
                            req_names.append(name)

                    if req_names:
                        test_case['requirement_name'] = ', '.join(req_names)
                    else:
                        test_case['requirement_name'] = '无关联需求'
                else:
                    test_case['requirement_name'] = '无关联需求'

        except Exception as e:
            print(f"获取需求信息失败: {e}")
            # 如果获取需求信息失败，设置默认值
            for test_case in test_cases:
                test_case['requirement_name'] = '无关联需求'

    def batch_delete_test_cases(self, test_case_ids: List[str], project_id: str) -> Dict:
        """批量删除测试用例"""
        results = {
            'success_count': 0,
            'failed_count': 0,
            'errors': []
        }
        
        for case_id in test_case_ids:
            try:
                success = self.delete_test_case(case_id, project_id)
                if success:
                    results['success_count'] += 1
                else:
                    results['failed_count'] += 1
                    results['errors'].append(f"测试用例 {case_id} 删除失败")
            except Exception as e:
                results['failed_count'] += 1
                results['errors'].append(f"测试用例 {case_id} 删除失败: {str(e)}")
        
        return results
    
    def get_test_case_types(self) -> List[str]:
        """获取测试用例类型列表"""
        return ['用户测试用例', '自动化测试用例']
    
    def get_test_case_statuses(self) -> List[str]:
        """获取测试用例状态列表"""
        return ['待执行', '执行中', '通过', '失败', '阻塞']
    
    def get_test_case_priorities(self) -> List[str]:
        """获取测试用例优先级列表"""
        return ['高', '中', '低']
    
    def load_test_case_prompt(self) -> str:
        """加载测试用例生成提示词"""
        prompt_file = os.path.join(self.prompts_path, 'test_case_generation.txt')
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception:
            raise FileNotFoundError(f"未找到提示词文件: test_case_generation.txt")
    
    def load_automation_prompt(self) -> str:
        """加载自动化测试用例生成提示词"""
        prompt_file = os.path.join(self.prompts_path, 'automation_test_generation.txt')
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception:
            raise FileNotFoundError(f"未找到提示词文件: automation_test_generation.txt")
    
    def update_test_case_prompt(self, prompt: str) -> bool:
        """更新测试用例生成提示词"""
        prompt_file = os.path.join(self.prompts_path, 'test_case_generation.txt')
        try:
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(prompt)
            return True
        except Exception as e:
            print(f"更新提示词失败: {e}")
            return False
    
    def update_automation_prompt(self, prompt: str) -> bool:
        """更新自动化测试用例生成提示词"""
        prompt_file = os.path.join(self.prompts_path, 'automation_test_generation.txt')
        try:
            with open(prompt_file, 'w', encoding='utf-8') as f:
                f.write(prompt)
            return True
        except Exception as e:
            print(f"更新提示词失败: {e}")
            return False

    def get_test_cases_by_requirement(self, requirement_id: str, project_id: str) -> List[Dict]:
        """获取指定需求的测试用例"""
        all_cases = self.storage.list('test_cases', project_id=project_id, page_size=1000)['records']

        related_cases = []
        for case in all_cases:
            requirement_ids = case.get('requirement_ids', [])
            if isinstance(requirement_ids, list) and requirement_id in requirement_ids:
                related_cases.append(case)
            elif isinstance(requirement_ids, str) and requirement_ids == requirement_id:
                related_cases.append(case)

        return related_cases

    def get_test_case_statistics(self, project_id: str) -> Dict:
        """获取测试用例统计信息"""
        all_cases = self.storage.list('test_cases', project_id=project_id, page_size=1000)['records']

        stats = {
            'total': len(all_cases),
            'by_type': {},
            'by_status': {},
            'by_priority': {}
        }

        for case in all_cases:
            # 按类型统计
            case_type = case.get('type', '未分类')
            stats['by_type'][case_type] = stats['by_type'].get(case_type, 0) + 1

            # 按状态统计
            status = case.get('status', '未设置')
            stats['by_status'][status] = stats['by_status'].get(status, 0) + 1

            # 按优先级统计
            priority = case.get('priority', '未设置')
            stats['by_priority'][priority] = stats['by_priority'].get(priority, 0) + 1

        return stats

    def execute_test_case(self, test_case_id: str, project_id: str,
                         execution_result: Dict) -> bool:
        """执行测试用例并记录结果"""
        try:
            # 更新测试用例状态和执行结果
            update_data = {
                'status': execution_result.get('status', '执行中'),
                'execution_result': execution_result.get('result', ''),
                'execution_notes': execution_result.get('notes', ''),
                'executed_at': execution_result.get('executed_at'),
                'executed_by': execution_result.get('executed_by', '')
            }

            return self.storage.update('test_cases', test_case_id, update_data, project_id)

        except Exception as e:
            print(f"记录测试执行结果失败: {e}")
            return False

    def validate_test_case_name(self, name: str, project_id: str, test_case_id: str = None) -> Dict:
        """验证测试用例名称"""
        if not name or not name.strip():
            return {'valid': False, 'message': '测试用例名称不能为空'}

        if len(name.strip()) > 200:
            return {'valid': False, 'message': '测试用例名称不能超过200个字符'}

        # 检查名称是否重复
        test_cases = self.storage.list('test_cases', project_id=project_id, page_size=1000)['records']
        for case in test_cases:
            if case['name'] == name.strip() and case['id'] != test_case_id:
                return {'valid': False, 'message': '测试用例名称已存在'}

        return {'valid': True, 'message': '测试用例名称可用'}

    def _generate_test_cases_multithreaded(self, requirements: List[Dict], knowledge_context: str,
                                         system_prompt: str, project_id: str, max_threads: int) -> Dict:
        """多线程生成测试用例"""
        import threading
        from concurrent.futures import ThreadPoolExecutor, as_completed
        import math

        all_test_cases = []
        errors = []
        results_lock = threading.Lock()

        # 将需求分组，每个线程处理一组
        chunk_size = max(1, math.ceil(len(requirements) / max_threads))
        requirement_chunks = [requirements[i:i + chunk_size]
                            for i in range(0, len(requirements), chunk_size)]

        def process_requirement_chunk(chunk):
            """处理一组需求"""
            try:
                print(f'[线程] 开始为 {len(chunk)} 个需求生成测试用例')

                # 为这组需求生成测试用例
                test_cases = self.llm_client.generate_test_cases(
                    chunk, knowledge_context, system_prompt
                )

                # 为每个测试用例添加项目和需求关联信息
                requirement_ids = [req['id'] for req in chunk]
                for test_case in test_cases:
                    test_case['project_id'] = project_id
                    test_case['requirement_ids'] = requirement_ids
                    test_case['status'] = '待执行'
                    test_case['type'] = test_case.get('type', '用户测试用例')

                print(f'[线程] 已生成 {len(test_cases)} 个测试用例')

                return {
                    'success': True,
                    'test_cases': test_cases
                }

            except Exception as e:
                return {
                    'success': False,
                    'error': f"处理需求组失败: {str(e)}"
                }

        # 使用线程池执行
        print(f"使用 {max_threads} 个线程并发处理 {len(requirement_chunks)} 组需求")

        with ThreadPoolExecutor(max_workers=max_threads) as executor:
            # 提交所有任务
            future_to_chunk = {executor.submit(process_requirement_chunk, chunk): chunk
                             for chunk in requirement_chunks}

            # 收集结果
            for future in as_completed(future_to_chunk):
                chunk = future_to_chunk[future]
                try:
                    result = future.result()

                    with results_lock:
                        if result['success']:
                            all_test_cases.extend(result['test_cases'])
                        else:
                            errors.append(result['error'])

                except Exception as e:
                    with results_lock:
                        errors.append(f"处理需求组异常: {str(e)}")

        print(f"多线程处理完成，共生成 {len(all_test_cases)} 个测试用例")

        return {
            'success': True,
            'test_cases': all_test_cases,
            'total_generated': len(all_test_cases),
            'errors': errors
        }
