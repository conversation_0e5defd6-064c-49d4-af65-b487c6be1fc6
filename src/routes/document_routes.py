#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, request, jsonify, render_template, session, send_file
from werkzeug.utils import secure_filename
import os
from ..document_manager import DocumentManager

def create_document_blueprint(document_manager:DocumentManager):
    """创建文档路由蓝图"""
    bp = Blueprint('documents', __name__, url_prefix='/documents')
    
    @bp.route('/')
    def index():
        """文档管理页面"""
        return render_template('documents/index.html')
    
    @bp.route('/api/list')
    def list_documents():
        """获取文档列表API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        search = request.args.get('search', '').strip()
        
        try:
            result = document_manager.list_documents(
                project_id=project_id,
                page=page,
                page_size=page_size,
                search=search if search else None
            )
            return jsonify({
                'success': True,
                'data': result
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取文档列表失败: {str(e)}'
            }), 500
    
    @bp.route('/api/upload', methods=['POST'])
    def upload_document():
        """上传文档API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': '没有选择文件'
            }), 400
        
        file = request.files['file']
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        
        try:
            document_id = document_manager.upload_document(
                project_id=project_id,
                file=file,
                name=name,
                description=description
            )
            
            return jsonify({
                'success': True,
                'data': {'id': document_id},
                'message': '文档上传成功'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'文档上传失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<document_id>')
    def get_document(document_id):
        """获取文档详情API"""
        project_id = session.get('current_project_id')
        
        try:
            document = document_manager.get_document(document_id, project_id)
            if not document:
                return jsonify({
                    'success': False,
                    'message': '文档不存在'
                }), 404
            
            return jsonify({
                'success': True,
                'data': document
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取文档详情失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<document_id>/content')
    def get_document_content(document_id):
        """获取文档内容API"""
        project_id = session.get('current_project_id')
        
        try:
            content = document_manager.get_document_content(document_id, project_id)
            if content is None:
                return jsonify({
                    'success': False,
                    'message': '文档不存在'
                }), 404
            
            return jsonify({
                'success': True,
                'data': {'content': content}
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取文档内容失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<document_id>/preview')
    def preview_document(document_id):
        """预览文档API"""
        project_id = session.get('current_project_id')
        
        try:
            content = document_manager.get_document_content(document_id, project_id)
            if content is None:
                return jsonify({
                    'success': False,
                    'message': '文档不存在'
                }), 404
            
            # 转换为HTML
            from ..utils.file_converter import FileConverter
            converter = FileConverter()
            html_content = converter.markdown_to_html(content)
            
            return html_content
        except Exception as e:
            return f'<div>预览失败: {str(e)}</div>'
    
    @bp.route('/api/<document_id>/sections')
    def get_document_sections(document_id):
        """获取文档章节API"""
        project_id = session.get('current_project_id')
        
        try:
            sections = document_manager.get_document_sections(document_id, project_id)
            return jsonify({
                'success': True,
                'data': sections
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取文档章节失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<document_id>/update', methods=['PUT'])
    def update_document(document_id):
        """更新文档API"""
        project_id = session.get('current_project_id')
        data = request.get_json()
        
        name = data.get('name', '').strip()
        description = data.get('description', '').strip()
        content = data.get('content')
        
        try:
            # 验证文档名称
            if name:
                validation = document_manager.validate_document_name(name, project_id, document_id)
                if not validation['valid']:
                    return jsonify({
                        'success': False,
                        'message': validation['message']
                    }), 400
            
            # 更新文档信息
            if name or description:
                success = document_manager.update_document(
                    document_id, project_id, name, description
                )
                if not success:
                    return jsonify({
                        'success': False,
                        'message': '更新文档信息失败'
                    }), 400
            
            # 更新文档内容
            if content is not None:
                success = document_manager.update_document_content(
                    document_id, content, project_id
                )
                if not success:
                    return jsonify({
                        'success': False,
                        'message': '更新文档内容失败'
                    }), 400
            
            return jsonify({
                'success': True,
                'message': '文档更新成功'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新文档失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<document_id>/delete', methods=['DELETE'])
    def delete_document(document_id):
        """删除文档API"""
        project_id = session.get('current_project_id')
        
        try:
            success = document_manager.delete_document(document_id, project_id)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '文档删除成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '文档删除失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'删除文档失败: {str(e)}'
            }), 500
    
    @bp.route('/api/validate_name', methods=['POST'])
    def validate_document_name():
        """验证文档名称API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        data = request.get_json()
        name = data.get('name', '').strip()
        document_id = data.get('document_id')
        
        try:
            validation = document_manager.validate_document_name(name, project_id, document_id)
            return jsonify({
                'success': True,
                'data': validation
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'验证文档名称失败: {str(e)}'
            }), 500
    
    return bp
