#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import sqlite3
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional

class StorageManager:
    """统一的数据存储管理器"""
    
    def __init__(self, config):
        self.config = config
        self.storage_type = config.STORAGE_TYPE
        self.data_path = config.DATA_PATH
        
        if self.storage_type == 'sqlite':
            self.db_path = os.path.join(self.data_path, 'intellitest.db')
            self._init_sqlite()
        else:
            self._ensure_json_directories()
    
    def _init_sqlite(self):
        """初始化SQLite数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建项目表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS projects (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                created_at TEXT,
                updated_at TEXT
            )
        ''')
        
        # 创建文档表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS documents (
                id TEXT PRIMARY KEY,
                project_id TEXT,
                name TEXT NOT NULL,
                file_path TEXT,
                file_type TEXT,
                content TEXT,
                created_at TEXT,
                updated_at TEXT,
                FOREIGN KEY (project_id) REFERENCES projects (id)
            )
        ''')
        
        # 创建需求功能点表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS requirements (
                id TEXT PRIMARY KEY,
                project_id TEXT,
                document_id TEXT,
                section TEXT,
                type TEXT,
                description TEXT,
                priority TEXT,
                status TEXT,
                created_at TEXT,
                updated_at TEXT,
                FOREIGN KEY (project_id) REFERENCES projects (id),
                FOREIGN KEY (document_id) REFERENCES documents (id)
            )
        ''')
        
        # 创建测试用例表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_cases (
                id TEXT PRIMARY KEY,
                project_id TEXT,
                requirement_id TEXT,
                name TEXT NOT NULL,
                type TEXT,
                description TEXT,
                steps TEXT,
                expected_result TEXT,
                status TEXT,
                created_at TEXT,
                updated_at TEXT,
                FOREIGN KEY (project_id) REFERENCES projects (id),
                FOREIGN KEY (requirement_id) REFERENCES requirements (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def _ensure_json_directories(self):
        """确保JSON存储目录存在"""
        directories = [
            'projects',
            'documents', 
            'requirements',
            'test_cases',
            'knowledge_base'
        ]
        
        for directory in directories:
            path = os.path.join(self.data_path, directory)
            os.makedirs(path, exist_ok=True)
    
    def _get_json_file_path(self, table: str, project_id: str = None) -> str:
        """获取JSON文件路径"""
        if project_id:
            return os.path.join(self.data_path, table, f"{project_id}.json")
        else:
            return os.path.join(self.data_path, f"{table}.json")
    
    def _load_json_data(self, file_path: str) -> List[Dict]:
        """加载JSON数据"""
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return []
        return []
    
    def _save_json_data(self, file_path: str, data: List[Dict]):
        """保存JSON数据"""
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def create(self, table: str, data: Dict, project_id: str = None) -> str:
        """创建记录"""
        record_id = str(uuid.uuid4())
        now = datetime.now().isoformat()
        
        record = {
            'id': record_id,
            **data,
            'created_at': now,
            'updated_at': now
        }
        
        if self.storage_type == 'sqlite':
            return self._create_sqlite(table, record)
        else:
            return self._create_json(table, record, project_id)
    
    def _create_sqlite(self, table: str, record: Dict) -> str:
        """SQLite创建记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        columns = ', '.join(record.keys())
        placeholders = ', '.join(['?' for _ in record])
        values = list(record.values())
        
        cursor.execute(f'INSERT INTO {table} ({columns}) VALUES ({placeholders})', values)
        conn.commit()
        conn.close()
        
        return record['id']
    
    def _create_json(self, table: str, record: Dict, project_id: str = None) -> str:
        """JSON创建记录"""
        if table == 'projects':
            file_path = self._get_json_file_path('projects')
            data = self._load_json_data(file_path)
            data.append(record)
            self._save_json_data(file_path, data)
        else:
            # 项目相关的数据存储在项目目录下
            if not project_id:
                project_id = record.get('project_id')
            
            file_path = self._get_json_file_path(table, project_id)
            data = self._load_json_data(file_path)
            data.append(record)
            self._save_json_data(file_path, data)
        
        return record['id']

    def read(self, table: str, record_id: str, project_id: str = None) -> Optional[Dict]:
        """读取单条记录"""
        if self.storage_type == 'sqlite':
            return self._read_sqlite(table, record_id)
        else:
            return self._read_json(table, record_id, project_id)

    def _read_sqlite(self, table: str, record_id: str) -> Optional[Dict]:
        """SQLite读取记录"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute(f'SELECT * FROM {table} WHERE id = ?', (record_id,))
        row = cursor.fetchone()
        conn.close()

        return dict(row) if row else None

    def _read_json(self, table: str, record_id: str, project_id: str = None) -> Optional[Dict]:
        """JSON读取记录"""
        if table == 'projects':
            file_path = self._get_json_file_path('projects')
            data = self._load_json_data(file_path)
            for record in data:
                if record['id'] == record_id:
                    return record
        else:
            if not project_id:
                # 如果没有提供project_id，需要在所有项目中查找
                projects_path = os.path.join(self.data_path, 'projects.json')
                projects = self._load_json_data(projects_path)
                for project in projects:
                    file_path = self._get_json_file_path(table, project['id'])
                    data = self._load_json_data(file_path)
                    for record in data:
                        if record['id'] == record_id:
                            return record
            else:
                file_path = self._get_json_file_path(table, project_id)
                data = self._load_json_data(file_path)
                for record in data:
                    if record['id'] == record_id:
                        return record

        return None

    def update(self, table: str, record_id: str, data: Dict, project_id: str = None) -> bool:
        """更新记录"""
        data['updated_at'] = datetime.now().isoformat()

        if self.storage_type == 'sqlite':
            return self._update_sqlite(table, record_id, data)
        else:
            return self._update_json(table, record_id, data, project_id)

    def _update_sqlite(self, table: str, record_id: str, data: Dict) -> bool:
        """SQLite更新记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        set_clause = ', '.join([f'{key} = ?' for key in data.keys()])
        values = list(data.values()) + [record_id]

        cursor.execute(f'UPDATE {table} SET {set_clause} WHERE id = ?', values)
        success = cursor.rowcount > 0
        conn.commit()
        conn.close()

        return success

    def _update_json(self, table: str, record_id: str, data: Dict, project_id: str = None) -> bool:
        """JSON更新记录"""
        if table == 'projects':
            file_path = self._get_json_file_path('projects')
            records = self._load_json_data(file_path)
            for i, record in enumerate(records):
                if record['id'] == record_id:
                    records[i].update(data)
                    self._save_json_data(file_path, records)
                    return True
        else:
            if not project_id:
                # 查找记录所在的项目
                existing_record = self.read(table, record_id)
                if existing_record:
                    project_id = existing_record.get('project_id')

            if project_id:
                file_path = self._get_json_file_path(table, project_id)
                records = self._load_json_data(file_path)
                for i, record in enumerate(records):
                    if record['id'] == record_id:
                        records[i].update(data)
                        self._save_json_data(file_path, records)
                        return True

        return False

    def delete(self, table: str, record_id: str, project_id: str = None) -> bool:
        """删除记录"""
        if self.storage_type == 'sqlite':
            return self._delete_sqlite(table, record_id)
        else:
            return self._delete_json(table, record_id, project_id)

    def _delete_sqlite(self, table: str, record_id: str) -> bool:
        """SQLite删除记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute(f'DELETE FROM {table} WHERE id = ?', (record_id,))
        success = cursor.rowcount > 0
        conn.commit()
        conn.close()

        return success

    def _delete_json(self, table: str, record_id: str, project_id: str = None) -> bool:
        """JSON删除记录"""
        if table == 'projects':
            file_path = self._get_json_file_path('projects')
            records = self._load_json_data(file_path)
            for i, record in enumerate(records):
                if record['id'] == record_id:
                    del records[i]
                    self._save_json_data(file_path, records)
                    return True
        else:
            if not project_id:
                # 查找记录所在的项目
                existing_record = self.read(table, record_id)
                if existing_record:
                    project_id = existing_record.get('project_id')

            if project_id:
                file_path = self._get_json_file_path(table, project_id)
                records = self._load_json_data(file_path)
                for i, record in enumerate(records):
                    if record['id'] == record_id:
                        del records[i]
                        self._save_json_data(file_path, records)
                        return True

        return False

    def list(self, table: str, project_id: str = None, filters: Dict = None,
             page: int = 1, page_size: int = 20) -> Dict:
        """列表查询"""
        if self.storage_type == 'sqlite':
            return self._list_sqlite(table, filters, page, page_size)
        else:
            return self._list_json(table, project_id, filters, page, page_size)

    def _list_sqlite(self, table: str, filters: Dict = None,
                     page: int = 1, page_size: int = 20) -> Dict:
        """SQLite列表查询"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        where_clause = ""
        params = []

        if filters:
            conditions = []
            for key, value in filters.items():
                if value is not None:
                    conditions.append(f"{key} = ?")
                    params.append(value)

            if conditions:
                where_clause = "WHERE " + " AND ".join(conditions)

        # 获取总数
        cursor.execute(f'SELECT COUNT(*) FROM {table} {where_clause}', params)
        total = cursor.fetchone()[0]

        # 获取分页数据
        offset = (page - 1) * page_size
        cursor.execute(f'SELECT * FROM {table} {where_clause} ORDER BY created_at DESC LIMIT ? OFFSET ?',
                      params + [page_size, offset])

        rows = cursor.fetchall()
        records = [dict(row) for row in rows]

        conn.close()

        return {
            'records': records,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        }

    def _list_json(self, table: str, project_id: str = None, filters: Dict = None,
                   page: int = 1, page_size: int = 20) -> Dict:
        """JSON列表查询"""
        if table == 'projects':
            file_path = self._get_json_file_path('projects')
            records = self._load_json_data(file_path)
        else:
            if project_id:
                file_path = self._get_json_file_path(table, project_id)
                records = self._load_json_data(file_path)
            else:
                records = []

        # 应用过滤器
        if filters:
            filtered_records = []
            for record in records:
                match = True
                for key, value in filters.items():
                    if value is not None and record.get(key) != value:
                        match = False
                        break
                if match:
                    filtered_records.append(record)
            records = filtered_records

        # 排序
        records.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        # 分页
        total = len(records)
        start = (page - 1) * page_size
        end = start + page_size
        page_records = records[start:end]

        return {
            'records': page_records,
            'total': total,
            'page': page,
            'page_size': page_size,
            'total_pages': (total + page_size - 1) // page_size
        }

    def search(self, table: str, query: str, project_id: str = None,
               fields: List[str] = None) -> List[Dict]:
        """搜索记录"""
        if self.storage_type == 'sqlite':
            return self._search_sqlite(table, query, fields)
        else:
            return self._search_json(table, query, project_id, fields)

    def _search_sqlite(self, table: str, query: str, fields: List[str] = None) -> List[Dict]:
        """SQLite搜索"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        if not fields:
            fields = ['name', 'description']

        conditions = []
        params = []
        for field in fields:
            conditions.append(f"{field} LIKE ?")
            params.append(f"%{query}%")

        where_clause = " OR ".join(conditions)
        cursor.execute(f'SELECT * FROM {table} WHERE {where_clause} ORDER BY created_at DESC', params)

        rows = cursor.fetchall()
        records = [dict(row) for row in rows]

        conn.close()
        return records

    def _search_json(self, table: str, query: str, project_id: str = None,
                     fields: List[str] = None) -> List[Dict]:
        """JSON搜索"""
        if not fields:
            fields = ['name', 'description']

        if table == 'projects':
            file_path = self._get_json_file_path('projects')
            records = self._load_json_data(file_path)
        else:
            if project_id:
                file_path = self._get_json_file_path(table, project_id)
                records = self._load_json_data(file_path)
            else:
                records = []

        # 搜索匹配的记录
        results = []
        query_lower = query.lower()

        for record in records:
            for field in fields:
                field_value = record.get(field, '')
                if isinstance(field_value, str) and query_lower in field_value.lower():
                    results.append(record)
                    break

        # 按创建时间排序
        results.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        return results
